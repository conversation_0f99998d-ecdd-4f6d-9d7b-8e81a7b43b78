import { ContentFlowLandingPage } from "@/components/contentflow/LandingPage";
import { setRequestLocale } from 'next-intl/server';
import type { Metadata } from "next";

// Add page-specific metadata
export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;

  return {
    title: locale === 'zh'
      ? "ContentFlow - AI驱动的内容创作平台"
      : "ContentFlow - AI-Powered Content Creation Platform",
    description: locale === 'zh'
      ? "统一管理多个社交平台，AI智能优化内容，数据驱动决策。专为内容创作者打造的一站式解决方案。"
      : "Unified management of multiple social platforms, AI-optimized content, data-driven decisions. A one-stop solution designed for content creators.",
  };
}

export default async function HomePage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;

  // 设置请求的 locale
  setRequestLocale(locale);

  return (
    <ContentFlowLandingPage />
  );
}
