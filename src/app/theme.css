@tailwind base;
@tailwind components;
@tailwind utilities;

:root  {
  /* ContentFlow Wedding Theme - Light Mode */
  --background: 0 0% 100%;             /* Pure white background */
  --foreground: 345 15% 15%;           /* Warm dark text */
  --card: 0 0% 100%;                   /* White cards */
  --card-foreground: 345 15% 15%;      /* Warm dark text on cards */
  --popover: 0 0% 100%;                /* White popover */
  --popover-foreground: 345 15% 15%;   /* Warm dark text on popover */
  --primary: 350 100% 88%;             /* Soft blush pink #FFD6E1 */
  --primary-foreground: 345 15% 15%;   /* Dark text on pink */
  --secondary: 30 50% 98%;             /* Warm cream background */
  --secondary-foreground: 345 15% 15%; /* Dark text on cream */
  --muted: 30 50% 96%;                 /* Muted cream background */
  --muted-foreground: 345 15% 45%;     /* Muted warm text */
  --accent: 350 100% 95%;              /* Light blush accent */
  --accent-foreground: 345 15% 15%;    /* Dark text on accent */
  --destructive: 0 84.2% 60.2%;        /* Error red */
  --destructive-foreground: 210 40% 98%; /* Light text on red */
  --border: 30 30% 92%;                /* Warm light border */
  --input: 30 30% 92%;                 /* Input border */
  --ring: 350 100% 88%;                /* Focus ring blush */
  --radius: 0.75rem;                   /* Softer border radius */

  /* ContentFlow Wedding-specific custom properties */
  --contentflow-blush: 350 100% 88%;        /* Primary blush pink */
  --contentflow-rose: 340 82% 52%;          /* Rose accent #D946EF */
  --contentflow-cream: 30 50% 98%;          /* Warm cream */
  --contentflow-champagne: 45 25% 85%;      /* Champagne gold */
  --contentflow-sage: 120 25% 75%;          /* Soft sage green */
  --contentflow-lavender: 270 50% 85%;      /* Soft lavender */
  --contentflow-warm-gray: 30 10% 85%;      /* Warm gray */
  --contentflow-text-warm: 345 15% 25%;     /* Warm text */
}

.dark  {
  /* ContentFlow Wedding Theme - Dark Mode */
  --background: 345 25% 8%;            /* Deep warm dark background */
  --foreground: 30 50% 95%;            /* Warm light text */
  --card: 345 25% 10%;                 /* Dark warm cards */
  --card-foreground: 30 50% 95%;       /* Warm light text on cards */
  --popover: 345 25% 10%;              /* Dark warm popover */
  --popover-foreground: 30 50% 95%;    /* Warm light text on popover */
  --primary: 350 100% 75%;             /* Bright blush for dark mode */
  --primary-foreground: 345 25% 8%;    /* Dark text on bright blush */
  --secondary: 345 20% 15%;            /* Dark warm secondary */
  --secondary-foreground: 30 50% 95%;  /* Light text on dark */
  --muted: 345 20% 15%;                /* Muted dark warm background */
  --muted-foreground: 30 25% 75%;      /* Muted warm light text */
  --accent: 345 20% 15%;               /* Dark warm accent */
  --accent-foreground: 30 50% 95%;     /* Light text on dark accent */
  --destructive: 0 62.8% 30.6%;        /* Dark mode error red */
  --destructive-foreground: 210 40% 98%; /* Light text on red */
  --border: 345 20% 20%;               /* Dark warm border */
  --input: 345 20% 20%;                /* Dark warm input border */
  --ring: 350 100% 75%;                /* Focus ring bright blush */

  /* Dark mode ContentFlow Wedding colors */
  --contentflow-blush: 350 100% 75%;        /* Bright blush for dark */
  --contentflow-rose: 340 82% 65%;          /* Bright rose accent */
  --contentflow-cream: 30 30% 20%;          /* Dark cream */
  --contentflow-champagne: 45 35% 35%;      /* Dark champagne */
  --contentflow-sage: 120 25% 45%;          /* Dark sage green */
  --contentflow-lavender: 270 50% 65%;      /* Bright lavender */
  --contentflow-warm-gray: 345 15% 25%;     /* Dark warm gray */
  --contentflow-text-warm: 30 50% 85%;      /* Warm light text */
}

@layer base {
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .tool-card {
    @apply relative bg-card border border-border p-5 rounded-xl transition-all duration-300;
  }
  
  .tool-card:hover {
    @apply shadow-md border-primary/20 -translate-y-1;
  }

  .tool-card-featured {
    @apply relative bg-gradient-to-br from-accent to-background border border-primary/20 p-5 rounded-xl transition-all duration-300;
  }
  
  .tool-card-featured:hover {
    @apply shadow-lg border-primary/40 -translate-y-1;
  }

  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-new {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-featured {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .badge-free {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-premium {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .nav-link {
    @apply flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground;
  }

  .nav-link-active {
    @apply bg-accent text-accent-foreground;
  }

  .search-input {
    @apply w-full bg-background/70 backdrop-blur-sm rounded-full px-5 py-3 border border-border focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none text-base;
  }

  .glass-card {
    @apply bg-white/70 backdrop-blur-sm border border-white/20 shadow-sm dark:bg-gray-900/70;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-md dark:bg-gray-900/80;
  }
  
  /* Improved category button styles */
  .category-button {
    @apply flex items-center gap-2 px-4 py-3 rounded-xl border border-sidebar-border bg-sidebar-background hover:bg-sidebar-hover hover:border-sidebar-primary/30 hover:shadow-sm transition-all;
  }
  
  .category-button-active {
    @apply border-sidebar-primary/50 bg-sidebar-accent shadow-sm;
  }
  
  /* Better form controls */
  .form-input {
    @apply rounded-md border border-input px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all;
  }
  
  /* Animation utilities */
  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }
  
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-md;
  }
  
  /* 侧边栏样式增强 */
  .sidebar-header {
    @apply border-b border-sidebar-border pb-3 mb-4;
  }
  
  .sidebar-title {
    @apply text-lg font-semibold text-sidebar-foreground;
  }
  
  .sidebar-category-icon {
    @apply text-sidebar-primary transition-colors;
  }
  
  .sidebar-category-name {
    @apply text-sidebar-foreground transition-colors;
  }
  
  .sidebar-category-item {
    @apply rounded-lg transition-all hover:bg-sidebar-hover p-2 flex items-center gap-3 cursor-pointer;
  }
  
  .sidebar-category-item-active {
    @apply bg-sidebar-accent text-sidebar-accent-foreground;
  }
}