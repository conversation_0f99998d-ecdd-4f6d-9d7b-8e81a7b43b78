'use client';

import React from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PenTool, Send, MessageCircle, Globe, Mail, Phone } from 'lucide-react';
import { useTranslations } from 'next-intl';

export const ContentFlowFooter = () => {
  const t = useTranslations('footer');

  return (
    <footer className="bg-gray-50 border-t border-gray-200 py-12 contentflow-footer-light dark:bg-gray-900 dark:border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div>
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 contentflow-button-primary rounded-lg flex items-center justify-center">
                <PenTool className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-xl font-bold contentflow-text-gradient">ContentFlow</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {t('brandDescription')}
            </p>

            {/* Newsletter Signup */}
            <div className="mb-6">
              <h4 className="text-sm font-semibold mb-3 text-gray-700 dark:text-gray-300">{t('newsletter.title')}</h4>
              <div className="flex">
                <Input
                  type="email"
                  placeholder={t('newsletter.placeholder')}
                  className="bg-white border-gray-300 text-gray-900 placeholder-gray-500 rounded-r-none dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:placeholder-gray-400"
                />
                <Button className="contentflow-button-primary rounded-l-none">
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Contact Links */}
            <div className="flex space-x-3">
              <Button variant="ghost" size="icon" className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20">
                <MessageCircle className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon" className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20">
                <Mail className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon" className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20">
                <Phone className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon" className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20">
                <Globe className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">{t('product.title')}</h4>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li><a href="#features" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('product.features')}</a></li>
              <li><a href="#pricing" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('product.pricing')}</a></li>
              <li><a href="/api" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('product.api')}</a></li>
              <li><a href="/integrations" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('product.integrations')}</a></li>
              <li><a href="/templates" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('product.templates')}</a></li>
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">{t('support.title')}</h4>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li><a href="/help" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('support.helpCenter')}</a></li>
              <li><a href="/tutorials" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('support.tutorials')}</a></li>
              <li><a href="/community" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('support.community')}</a></li>
              <li><a href="/contact" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('support.contact')}</a></li>
              <li><a href="/status" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('support.status')}</a></li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">{t('company.title')}</h4>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li><a href="/about" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('company.about')}</a></li>
              <li><a href="/careers" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('company.careers')}</a></li>
              <li><a href="/blog" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('company.blog')}</a></li>
              <li><a href="/press" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('company.press')}</a></li>
              <li><a href="/partners" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('company.partners')}</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-200 dark:border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 md:mb-0">
              {t('copyright')}
            </p>
            <div className="flex space-x-6 text-sm text-gray-600 dark:text-gray-400">
              <a href="/privacy" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('legal.privacy')}</a>
              <a href="/terms" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('legal.terms')}</a>
              <a href="/cookies" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">{t('legal.cookies')}</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
