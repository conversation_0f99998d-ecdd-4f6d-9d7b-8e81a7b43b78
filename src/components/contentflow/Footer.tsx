'use client';

import React from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PenTool, Send, MessageCircle, Globe, Mail, Phone } from 'lucide-react';

export const ContentFlowFooter = () => {
  return (
    <footer className="bg-gray-50 border-t border-gray-200 py-12 contentflow-footer-light dark:bg-gray-900 dark:border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div>
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 contentflow-button-primary rounded-lg flex items-center justify-center">
                <PenTool className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-xl font-bold contentflow-text-gradient">ContentFlow</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              AI驱动的内容创作与发布管理平台，让内容创作变得简单高效。
            </p>
            
            {/* Newsletter Signup */}
            <div className="mb-6">
              <h4 className="text-sm font-semibold mb-3 text-gray-700 dark:text-gray-300">订阅我们的更新</h4>
              <div className="flex">
                <Input
                  type="email"
                  placeholder="输入您的邮箱"
                  className="bg-white border-gray-300 text-gray-900 placeholder-gray-500 rounded-r-none dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:placeholder-gray-400"
                />
                <Button className="contentflow-button-primary rounded-l-none">
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Contact Links */}
            <div className="flex space-x-3">
              <Button variant="ghost" size="icon" className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20">
                <MessageCircle className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon" className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20">
                <Mail className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon" className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20">
                <Phone className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon" className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20">
                <Globe className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">产品</h4>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li><a href="#features" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">功能特色</a></li>
              <li><a href="#pricing" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">定价方案</a></li>
              <li><a href="/api" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">API文档</a></li>
              <li><a href="/integrations" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">集成应用</a></li>
              <li><a href="/templates" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">内容模板</a></li>
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">支持</h4>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li><a href="/help" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">帮助中心</a></li>
              <li><a href="/tutorials" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">使用教程</a></li>
              <li><a href="/community" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">社区论坛</a></li>
              <li><a href="/contact" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">联系我们</a></li>
              <li><a href="/status" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">服务状态</a></li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">公司</h4>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li><a href="/about" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">关于我们</a></li>
              <li><a href="/careers" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">招聘信息</a></li>
              <li><a href="/blog" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">博客</a></li>
              <li><a href="/press" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">媒体报道</a></li>
              <li><a href="/partners" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">合作伙伴</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-200 dark:border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 md:mb-0">
              &copy; 2024 ContentFlow. All rights reserved.
            </p>
            <div className="flex space-x-6 text-sm text-gray-600 dark:text-gray-400">
              <a href="/privacy" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">隐私政策</a>
              <a href="/terms" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">服务条款</a>
              <a href="/cookies" className="hover:text-blue-600 transition-colors dark:hover:text-blue-400">Cookie政策</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
