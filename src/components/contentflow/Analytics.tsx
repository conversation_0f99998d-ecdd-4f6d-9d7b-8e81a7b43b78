'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Heart,
  MessageCircle,
  Share2,
  Eye,
  Calendar,
  Download,
  Filter,
  Instagram,
  Twitter,
  Linkedin,
  Youtube
} from 'lucide-react';

export const ContentFlowAnalytics = () => {
  const overviewMetrics = [
    {
      title: "总覆盖人数",
      value: "156.8K",
      change: "+12.5%",
      trend: "up",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      title: "总互动数",
      value: "23.4K",
      change: "+18.2%",
      trend: "up",
      icon: Heart,
      color: "text-red-600",
      bgColor: "bg-red-50"
    },
    {
      title: "平均互动率",
      value: "6.8%",
      change: "+2.1%",
      trend: "up",
      icon: TrendingUp,
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      title: "内容发布数",
      value: "47",
      change: "+5",
      trend: "up",
      icon: BarChart3,
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    }
  ];

  const platformMetrics = [
    {
      platform: "Instagram",
      icon: Instagram,
      color: "bg-pink-500",
      followers: "15.2K",
      reach: "45.6K",
      engagement: "8.5%",
      posts: 18,
      topPost: "5个提升Instagram互动率的实用技巧"
    },
    {
      platform: "TikTok",
      icon: Youtube,
      color: "bg-red-500",
      followers: "8.7K",
      reach: "32.1K",
      engagement: "12.3%",
      posts: 12,
      topPost: "TikTok算法解密：获得更多曝光的秘诀"
    },
    {
      platform: "LinkedIn",
      icon: Linkedin,
      color: "bg-blue-600",
      followers: "3.2K",
      reach: "18.4K",
      engagement: "5.7%",
      posts: 8,
      topPost: "LinkedIn个人品牌建设完整指南"
    },
    {
      platform: "Twitter",
      icon: Twitter,
      color: "bg-blue-400",
      followers: "5.1K",
      reach: "12.8K",
      engagement: "4.2%",
      posts: 9,
      topPost: "内容营销的未来趋势分析"
    }
  ];

  const topContent = [
    {
      title: "5个提升Instagram互动率的实用技巧",
      platform: "Instagram",
      publishDate: "2024-01-15",
      reach: 15678,
      likes: 1234,
      comments: 89,
      shares: 45,
      engagementRate: 8.5,
      icon: Instagram,
      color: "bg-pink-500"
    },
    {
      title: "TikTok算法解密：获得更多曝光的秘诀",
      platform: "TikTok",
      publishDate: "2024-01-12",
      reach: 12456,
      likes: 987,
      comments: 156,
      shares: 67,
      engagementRate: 7.2,
      icon: Youtube,
      color: "bg-red-500"
    },
    {
      title: "LinkedIn个人品牌建设完整指南",
      platform: "LinkedIn",
      publishDate: "2024-01-10",
      reach: 18234,
      likes: 756,
      comments: 234,
      shares: 123,
      engagementRate: 6.8,
      icon: Linkedin,
      color: "bg-blue-600"
    }
  ];

  return (
    <div className="space-y-6 p-6">
      {/* 顶部标题栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">数据分析中心</h1>
          <p className="text-gray-600 mt-1">深度分析内容表现，洞察受众行为</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            筛选
          </Button>
          <Button variant="outline">
            <Calendar className="w-4 h-4 mr-2" />
            时间范围
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Download className="w-4 h-4 mr-2" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 概览指标 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {overviewMetrics.map((metric, index) => (
          <Card key={index} className="border-0 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{metric.value}</p>
                  <div className="flex items-center text-green-600">
                    <TrendingUp className="w-4 h-4 mr-1" />
                    <span className="text-sm font-medium">{metric.change}</span>
                  </div>
                </div>
                <div className={`p-3 rounded-full ${metric.bgColor}`}>
                  <metric.icon className={`w-6 h-6 ${metric.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 详细分析 */}
      <Tabs defaultValue="platforms" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="platforms">平台分析</TabsTrigger>
          <TabsTrigger value="content">内容表现</TabsTrigger>
          <TabsTrigger value="audience">受众洞察</TabsTrigger>
        </TabsList>

        <TabsContent value="platforms" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {platformMetrics.map((platform, index) => (
              <Card key={index} className="border-0 shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <div className={`p-2 rounded-lg ${platform.color} text-white mr-3`}>
                      <platform.icon className="w-5 h-5" />
                    </div>
                    {platform.platform}
                  </CardTitle>
                  <CardDescription>{platform.followers} 关注者</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <p className="text-2xl font-bold text-gray-900">{platform.reach}</p>
                      <p className="text-sm text-gray-600">覆盖人数</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-900">{platform.engagement}</p>
                      <p className="text-sm text-gray-600">互动率</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-900">{platform.posts}</p>
                      <p className="text-sm text-gray-600">发布数</p>
                    </div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm font-medium text-gray-900 mb-1">最佳表现内容</p>
                    <p className="text-sm text-gray-600">{platform.topPost}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle>内容表现排行榜</CardTitle>
              <CardDescription>按互动率排序的最佳表现内容</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topContent.map((content, index) => (
                  <div key={index} className="p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <div className={`p-2 rounded-lg ${content.color} text-white`}>
                            <content.icon className="w-4 h-4" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">{content.title}</h4>
                            <p className="text-sm text-gray-500">{content.publishDate}</p>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3">
                          <div className="text-center">
                            <div className="flex items-center justify-center space-x-1">
                              <Eye className="w-4 h-4 text-purple-500" />
                              <span className="text-sm font-medium">{content.reach.toLocaleString()}</span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">覆盖</p>
                          </div>
                          <div className="text-center">
                            <div className="flex items-center justify-center space-x-1">
                              <Heart className="w-4 h-4 text-red-500" />
                              <span className="text-sm font-medium">{content.likes.toLocaleString()}</span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">点赞</p>
                          </div>
                          <div className="text-center">
                            <div className="flex items-center justify-center space-x-1">
                              <MessageCircle className="w-4 h-4 text-blue-500" />
                              <span className="text-sm font-medium">{content.comments}</span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">评论</p>
                          </div>
                          <div className="text-center">
                            <div className="flex items-center justify-center space-x-1">
                              <Share2 className="w-4 h-4 text-green-500" />
                              <span className="text-sm font-medium">{content.shares}</span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">分享</p>
                          </div>
                        </div>
                      </div>
                      <div className="text-right ml-4">
                        <Badge variant="secondary" className="mb-2">
                          {content.engagementRate}% 互动率
                        </Badge>
                        <Button variant="ghost" size="sm" className="block">
                          查看详情
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audience" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>受众年龄分布</CardTitle>
                <CardDescription>按年龄段分析受众构成</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">18-24岁</span>
                    <span className="text-sm font-medium">35%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">25-34岁</span>
                    <span className="text-sm font-medium">42%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">35-44岁</span>
                    <span className="text-sm font-medium">18%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">45岁以上</span>
                    <span className="text-sm font-medium">5%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>活跃时间分析</CardTitle>
                <CardDescription>受众最活跃的时间段</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">上午 9-12点</span>
                    <span className="text-sm font-medium">25%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">下午 1-5点</span>
                    <span className="text-sm font-medium">45%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">晚上 6-9点</span>
                    <span className="text-sm font-medium">30%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
