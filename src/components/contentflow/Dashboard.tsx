'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  Users, 
  Target, 
  Plus,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  Calendar,
  BarChart3,
  Heart,
  MessageCircle,
  Share2,
  Eye,
  Zap,
  Clock,
  CheckCircle,
  Sparkles,
  Bell,
  Settings
} from 'lucide-react';

export const ContentFlowDashboard = () => {
  // 核心数据指标
  const keyMetrics = [
    { 
      title: "总互动量", 
      value: "12.5K", 
      change: "+15%", 
      trend: "up",
      icon: Heart, 
      color: "text-red-500",
      bgColor: "bg-red-50",
      description: "本月总互动数"
    },
    { 
      title: "覆盖人数", 
      value: "45.2K", 
      change: "+8%", 
      trend: "up",
      icon: Users, 
      color: "text-blue-500",
      bgColor: "bg-blue-50",
      description: "独立访客数量"
    },
    { 
      title: "转化率", 
      value: "3.2%", 
      change: "+0.5%", 
      trend: "up",
      icon: Target, 
      color: "text-green-500",
      bgColor: "bg-green-50",
      description: "点击转化效果"
    },
    { 
      title: "AI优化建议", 
      value: "8", 
      change: "待处理", 
      trend: "neutral",
      icon: Sparkles, 
      color: "text-purple-500",
      bgColor: "bg-purple-50",
      description: "智能优化提示"
    },
  ];

  // 最佳表现内容
  const topPerformingContent = [
    {
      id: 1,
      title: "5个提升Instagram互动率的实用技巧",
      platform: "Instagram",
      publishTime: "2小时前",
      metrics: {
        likes: 1234,
        comments: 89,
        shares: 45,
        reach: 15678,
        engagementRate: 8.5
      },
      status: "published",
      icon: Instagram,
      color: "bg-pink-500",
      trend: "up"
    },
    {
      id: 2,
      title: "TikTok算法解密：获得更多曝光的秘诀",
      platform: "TikTok",
      publishTime: "5小时前",
      metrics: {
        likes: 987,
        comments: 156,
        shares: 67,
        reach: 12456,
        engagementRate: 7.2
      },
      status: "viral",
      icon: Youtube,
      color: "bg-red-500",
      trend: "up"
    },
    {
      id: 3,
      title: "LinkedIn个人品牌建设完整指南",
      platform: "LinkedIn",
      publishTime: "1天前",
      metrics: {
        likes: 756,
        comments: 234,
        shares: 123,
        reach: 18234,
        engagementRate: 6.8
      },
      status: "published",
      icon: Linkedin,
      color: "bg-blue-600",
      trend: "stable"
    },
  ];

  // 账户连接状态
  const connectedAccounts = [
    { 
      platform: "Instagram", 
      username: "@your_username", 
      followers: "15.2K", 
      status: "active", 
      lastSync: "2分钟前",
      health: 100,
      icon: Instagram, 
      color: "bg-pink-500" 
    },
    { 
      platform: "TikTok", 
      username: "@your_tiktok", 
      followers: "8.7K", 
      status: "active", 
      lastSync: "5分钟前",
      health: 95,
      icon: Youtube, 
      color: "bg-red-500" 
    },
    { 
      platform: "LinkedIn", 
      username: "@your_linkedin", 
      followers: "3.2K", 
      status: "warning", 
      lastSync: "2小时前",
      health: 75,
      icon: Linkedin, 
      color: "bg-blue-600" 
    },
    { 
      platform: "Twitter", 
      username: "@your_twitter", 
      followers: "5.1K", 
      status: "inactive", 
      lastSync: "1天前",
      health: 40,
      icon: Twitter, 
      color: "bg-blue-400" 
    },
  ];

  // 即将发布的内容
  const upcomingContent = [
    { 
      id: 1,
      title: "内容营销策略深度解析", 
      scheduledTime: "今天 14:30", 
      platforms: ["Instagram", "TikTok"],
      type: "image",
      aiScore: 8.5,
      status: "ready"
    },
    { 
      id: 2,
      title: "如何制作病毒式传播的短视频", 
      scheduledTime: "明天 09:00", 
      platforms: ["TikTok", "Instagram"],
      type: "video",
      aiScore: 9.2,
      status: "optimizing"
    },
    { 
      id: 3,
      title: "个人品牌建设的10个关键步骤", 
      scheduledTime: "明天 15:00", 
      platforms: ["LinkedIn"],
      type: "article",
      aiScore: 7.8,
      status: "ready"
    },
  ];

  return (
    <div className="space-y-6 p-6">
      {/* 顶部欢迎区域 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">ContentFlow 工作台</h1>
          <p className="text-gray-600 mt-1">AI驱动的内容创作与发布管理中心</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Bell className="w-4 h-4 mr-2" />
            通知
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="w-4 h-4 mr-2" />
            设置
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            创建内容
          </Button>
        </div>
      </div>

      {/* 核心指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {keyMetrics.map((metric, index) => (
          <Card key={index} className="border-0 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{metric.value}</p>
                  <div className="flex items-center space-x-2">
                    {metric.trend === "up" && (
                      <div className="flex items-center text-green-600">
                        <TrendingUp className="w-4 h-4 mr-1" />
                        <span className="text-sm font-medium">{metric.change}</span>
                      </div>
                    )}
                    {metric.trend === "neutral" && (
                      <span className="text-sm text-orange-600 font-medium">{metric.change}</span>
                    )}
                  </div>
                  <p className="text-xs text-gray-500">{metric.description}</p>
                </div>
                <div className={`p-3 rounded-full ${metric.bgColor}`}>
                  <metric.icon className={`w-6 h-6 ${metric.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 内容表现排行榜 */}
        <div className="lg:col-span-2">
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                内容表现排行榜
              </CardTitle>
              <CardDescription>最近发布内容的实时表现数据</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {topPerformingContent.map((content, index) => (
                <div key={content.id} className="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className={`p-2 rounded-lg ${content.color} text-white`}>
                          <content.icon className="w-4 h-4" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900">{content.title}</h4>
                          <p className="text-sm text-gray-500">{content.publishTime}</p>
                        </div>
                        {content.status === "viral" && (
                          <Badge className="bg-orange-100 text-orange-800">🔥 爆款</Badge>
                        )}
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3">
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <Heart className="w-4 h-4 text-red-500" />
                            <span className="text-sm font-medium">{content.metrics.likes.toLocaleString()}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">点赞</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <MessageCircle className="w-4 h-4 text-blue-500" />
                            <span className="text-sm font-medium">{content.metrics.comments}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">评论</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <Share2 className="w-4 h-4 text-green-500" />
                            <span className="text-sm font-medium">{content.metrics.shares}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">分享</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <Eye className="w-4 h-4 text-purple-500" />
                            <span className="text-sm font-medium">{content.metrics.reach.toLocaleString()}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">覆盖</p>
                        </div>
                      </div>
                    </div>
                    <div className="text-right ml-4">
                      <Badge variant="secondary" className="mb-2">
                        {content.metrics.engagementRate}% 互动率
                      </Badge>
                      <Button variant="ghost" size="sm" className="block">
                        查看详情
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* 右侧边栏 */}
        <div className="space-y-6">
          {/* AI 智能建议 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Sparkles className="w-5 h-5 mr-2 text-purple-500" />
                AI智能建议
              </CardTitle>
              <CardDescription>基于数据分析的优化建议</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="p-3 rounded-lg bg-purple-50 border border-purple-200">
                <div className="flex items-start space-x-2">
                  <Zap className="w-4 h-4 text-purple-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-purple-900">最佳发布时间</p>
                    <p className="text-xs text-purple-700">建议在下午3-5点发布，用户活跃度最高</p>
                  </div>
                </div>
              </div>
              <div className="p-3 rounded-lg bg-blue-50 border border-blue-200">
                <div className="flex items-start space-x-2">
                  <Target className="w-4 h-4 text-blue-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">标题优化</p>
                    <p className="text-xs text-blue-700">添加数字和问号可提升点击率38%</p>
                  </div>
                </div>
              </div>
              <div className="p-3 rounded-lg bg-green-50 border border-green-200">
                <div className="flex items-start space-x-2">
                  <Heart className="w-4 h-4 text-green-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-green-900">互动优化</p>
                    <p className="text-xs text-green-700">在内容末尾添加问题可增加评论数</p>
                  </div>
                </div>
              </div>
              <Button variant="outline" size="sm" className="w-full">
                查看更多建议
              </Button>
            </CardContent>
          </Card>

          {/* 待发布内容 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                待发布内容
              </CardTitle>
              <CardDescription>即将发布的内容安排</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {upcomingContent.map((content, index) => (
                <div key={content.id} className="p-3 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 text-sm">{content.title}</h4>
                    {content.status === "optimizing" && (
                      <Badge variant="secondary" className="text-xs">
                        <Sparkles className="w-3 h-3 mr-1" />
                        AI优化中
                      </Badge>
                    )}
                    {content.status === "ready" && (
                      <Badge className="bg-green-100 text-green-800 text-xs">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        就绪
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
                    <Clock className="w-3 h-3" />
                    <span>{content.scheduledTime}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex space-x-1">
                      {content.platforms.map((platform, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {platform}
                        </Badge>
                      ))}
                    </div>
                    <div className="flex items-center space-x-1">
                      <Sparkles className="w-3 h-3 text-purple-500" />
                      <span className="text-xs text-gray-600">AI评分: {content.aiScore}</span>
                    </div>
                  </div>
                </div>
              ))}
              <Button variant="outline" size="sm" className="w-full">
                查看发布计划
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 账户连接状态 */}
      <Card className="border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Users className="w-5 h-5 mr-2" />
              社交账户连接状态
            </div>
            <Button variant="outline" size="sm">管理账户</Button>
          </CardTitle>
          <CardDescription>您的社交媒体账户连接和同步状态</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {connectedAccounts.map((account, index) => (
              <div key={index} className="p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-3">
                  <div className={`p-2 rounded-lg ${account.color} text-white`}>
                    <account.icon className="w-4 h-4" />
                  </div>
                  <div className="flex items-center space-x-1">
                    {account.status === 'active' && <div className="w-2 h-2 bg-green-500 rounded-full" />}
                    {account.status === 'warning' && <div className="w-2 h-2 bg-yellow-500 rounded-full" />}
                    {account.status === 'inactive' && <div className="w-2 h-2 bg-red-500 rounded-full" />}
                    <span className="text-xs text-gray-600 capitalize">{account.status}</span>
                  </div>
                </div>
                <h4 className="font-semibold text-gray-900">{account.platform}</h4>
                <p className="text-sm text-gray-600">{account.username}</p>
                <p className="text-sm text-gray-600 mt-1">{account.followers} 关注者</p>
                <div className="mt-3">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-500">连接健康度</span>
                    <span className="text-xs text-gray-600">{account.health}%</span>
                  </div>
                  <Progress value={account.health} className="h-1" />
                </div>
                <p className="text-xs text-gray-500 mt-2">最后同步: {account.lastSync}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
