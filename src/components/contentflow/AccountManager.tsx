'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import {
  Plus,
  Settings,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  Users,
  TrendingUp,
  Calendar,
  BarChart3
} from 'lucide-react';

interface AccountsMessages {
  title: string;
  subtitle: string;
  addAccount: string;
  refreshAll: string;
  platform: string;
  username: string;
  status: string;
  lastSync: string;
  health: string;
  autoPost: string;
  followers: string;
  following: string;
  posts: string;
  reach: string;
  engagement: string;
  growth: string;
  active: string;
  warning: string;
  error: string;
  disconnected: string;
  connected: string;
  connecting: string;
  syncNow: string;
  settings: string;
  disconnect: string;
  reconnect: string;
  accountHealth: string;
  performanceMetrics: string;
  quickActions: string;
  accountSynced: string;
  accountDisconnected: string;
  accountReconnected: string;
  autoPostEnabled: string;
  autoPostDisabled: string;
  minutes: string;
  hours: string;
  days: string;
}

interface ContentFlowAccountsProps {
  accounts: AccountsMessages;
}

export const ContentFlowAccounts = ({ accounts: t }: ContentFlowAccountsProps) => {
  const [accounts, setAccounts] = useState([
    {
      id: 1,
      platform: "Instagram",
      username: "@your_username",
      displayName: "Your Brand",
      followers: 15200,
      following: 1250,
      posts: 342,
      status: "active",
      lastSync: `2 ${t.minutes}`,
      health: 100,
      autoPost: true,
      icon: Instagram,
      color: "bg-pink-500",
      metrics: {
        reach: 45600,
        engagement: 8.5,
        growth: 12.3
      }
    },
    {
      id: 2,
      platform: "TikTok",
      username: "@your_tiktok",
      displayName: "Your TikTok",
      followers: 8700,
      following: 890,
      posts: 156,
      status: "active",
      lastSync: `5 ${t.minutes}`,
      health: 95,
      autoPost: true,
      icon: Youtube,
      color: "bg-red-500",
      metrics: {
        reach: 32100,
        engagement: 12.3,
        growth: 18.7
      }
    },
    {
      id: 3,
      platform: "LinkedIn",
      username: "@your_linkedin",
      displayName: "Your Professional",
      followers: 3200,
      following: 2100,
      posts: 89,
      status: "warning",
      lastSync: `2 ${t.hours}`,
      health: 75,
      autoPost: false,
      icon: Linkedin,
      color: "bg-blue-600",
      metrics: {
        reach: 18400,
        engagement: 5.7,
        growth: 8.2
      }
    },
    {
      id: 4,
      platform: "Twitter",
      username: "@your_twitter",
      displayName: "Your Twitter",
      followers: 5100,
      following: 1800,
      posts: 1250,
      status: "inactive",
      lastSync: `1 ${t.days}`,
      health: 40,
      autoPost: false,
      icon: Twitter,
      color: "bg-blue-400",
      metrics: {
        reach: 12800,
        engagement: 4.2,
        growth: -2.1
      }
    }
  ]);

  const availablePlatforms = [
    { name: "YouTube", icon: Youtube, color: "bg-red-600", connected: false },
    { name: "Facebook", icon: Users, color: "bg-blue-700", connected: false },
    { name: "Pinterest", icon: BarChart3, color: "bg-red-700", connected: false },
    { name: "小红书", icon: TrendingUp, color: "bg-red-400", connected: false }
  ];

  const handleSync = (accountId: number) => {
    setAccounts(accounts.map(account => 
      account.id === accountId 
        ? { ...account, lastSync: "刚刚", health: Math.min(100, account.health + 10) }
        : account
    ));
    toast.success("账户同步成功");
  };

  const handleToggleAutoPost = (accountId: number) => {
    setAccounts(accounts.map(account => 
      account.id === accountId 
        ? { ...account, autoPost: !account.autoPost }
        : account
    ));
    toast.success("自动发布设置已更新");
  };

  const handleConnectPlatform = (platformName: string) => {
    toast.success(`正在连接${platformName}...`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'inactive': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      case 'inactive': return <XCircle className="w-4 h-4" />;
      default: return <XCircle className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* 顶部标题栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">账户管理中心</h1>
          <p className="text-gray-600 mt-1">管理您的社交媒体账户连接和设置</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            全部同步
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            连接账户
          </Button>
        </div>
      </div>

      {/* 已连接账户 */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">已连接账户</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {accounts.map((account) => (
            <Card key={account.id} className="border-0 shadow-sm hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-3 rounded-lg ${account.color} text-white`}>
                      <account.icon className="w-6 h-6" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{account.platform}</CardTitle>
                      <CardDescription>{account.username}</CardDescription>
                    </div>
                  </div>
                  <Badge className={`${getStatusColor(account.status)} text-xs`}>
                    {getStatusIcon(account.status)}
                    <span className="ml-1 capitalize">{account.status}</span>
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 账户统计 */}
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-lg font-bold text-gray-900">{account.followers.toLocaleString()}</p>
                    <p className="text-xs text-gray-600">关注者</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-gray-900">{account.following.toLocaleString()}</p>
                    <p className="text-xs text-gray-600">关注中</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-gray-900">{account.posts}</p>
                    <p className="text-xs text-gray-600">发布数</p>
                  </div>
                </div>

                {/* 表现指标 */}
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="p-2 bg-blue-50 rounded">
                    <p className="text-sm font-bold text-blue-600">{account.metrics.reach.toLocaleString()}</p>
                    <p className="text-xs text-gray-600">月覆盖</p>
                  </div>
                  <div className="p-2 bg-green-50 rounded">
                    <p className="text-sm font-bold text-green-600">{account.metrics.engagement}%</p>
                    <p className="text-xs text-gray-600">互动率</p>
                  </div>
                  <div className="p-2 bg-purple-50 rounded">
                    <p className={`text-sm font-bold ${account.metrics.growth >= 0 ? 'text-purple-600' : 'text-red-600'}`}>
                      {account.metrics.growth >= 0 ? '+' : ''}{account.metrics.growth}%
                    </p>
                    <p className="text-xs text-gray-600">增长率</p>
                  </div>
                </div>

                {/* 连接健康度 */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">连接健康度</span>
                    <span className="text-sm font-medium">{account.health}%</span>
                  </div>
                  <Progress value={account.health} className="h-2" />
                </div>

                {/* 设置选项 */}
                <div className="flex items-center justify-between pt-2 border-t">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={account.autoPost}
                      onCheckedChange={() => handleToggleAutoPost(account.id)}
                    />
                    <span className="text-sm text-gray-600">自动发布</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" onClick={() => handleSync(account.id)}>
                      <RefreshCw className="w-3 h-3 mr-1" />
                      同步
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="w-3 h-3 mr-1" />
                      设置
                    </Button>
                  </div>
                </div>

                <p className="text-xs text-gray-500">最后同步: {account.lastSync}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* 可连接平台 */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">添加更多平台</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {availablePlatforms.map((platform, index) => (
            <Card key={index} className="border-0 shadow-sm hover:shadow-md transition-shadow cursor-pointer" onClick={() => handleConnectPlatform(platform.name)}>
              <CardContent className="p-6 text-center">
                <div className={`w-12 h-12 ${platform.color} rounded-lg flex items-center justify-center mx-auto mb-3`}>
                  <platform.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{platform.name}</h3>
                <Button variant="outline" size="sm" className="w-full">
                  <Plus className="w-3 h-3 mr-1" />
                  连接
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* 账户统计概览 */}
      <Card className="border-0 shadow-sm">
        <CardHeader>
          <CardTitle>账户统计概览</CardTitle>
          <CardDescription>所有连接账户的综合数据</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {accounts.reduce((sum, acc) => sum + acc.followers, 0).toLocaleString()}
              </div>
              <p className="text-sm text-gray-600">总关注者</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {accounts.reduce((sum, acc) => sum + acc.metrics.reach, 0).toLocaleString()}
              </div>
              <p className="text-sm text-gray-600">月总覆盖</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {(accounts.reduce((sum, acc) => sum + acc.metrics.engagement, 0) / accounts.length).toFixed(1)}%
              </div>
              <p className="text-sm text-gray-600">平均互动率</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {accounts.filter(acc => acc.status === 'active').length}
              </div>
              <p className="text-sm text-gray-600">活跃账户</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
