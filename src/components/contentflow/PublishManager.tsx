'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Calendar,
  Clock,
  Plus,
  Edit,
  Trash2,
  Play,
  Pause,
  CheckCircle,
  AlertCircle,
  Sparkles,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  Eye,
  BarChart3
} from 'lucide-react';

export const ContentFlowPublish = () => {
  const [selectedTab, setSelectedTab] = useState('scheduled');

  const scheduledContent = [
    {
      id: 1,
      title: "内容营销策略深度解析",
      scheduledTime: "2024-01-20 14:30",
      platforms: ["Instagram", "TikTok"],
      status: "ready",
      aiScore: 8.5,
      estimatedReach: 12500,
      type: "image",
      preview: "深入探讨现代内容营销的核心策略..."
    },
    {
      id: 2,
      title: "如何制作病毒式传播的短视频",
      scheduledTime: "2024-01-21 09:00",
      platforms: ["TikTok", "Instagram"],
      status: "optimizing",
      aiScore: 9.2,
      estimatedReach: 18700,
      type: "video",
      preview: "分享制作爆款短视频的实用技巧..."
    },
    {
      id: 3,
      title: "个人品牌建设的10个关键步骤",
      scheduledTime: "2024-01-21 15:00",
      platforms: ["LinkedIn"],
      status: "ready",
      aiScore: 7.8,
      estimatedReach: 5200,
      type: "article",
      preview: "系统性地构建个人品牌影响力..."
    },
    {
      id: 4,
      title: "社交媒体算法解密",
      scheduledTime: "2024-01-22 10:30",
      platforms: ["Twitter", "LinkedIn"],
      status: "draft",
      aiScore: 8.1,
      estimatedReach: 8900,
      type: "text",
      preview: "揭秘各大平台算法运作机制..."
    }
  ];

  const publishedContent = [
    {
      id: 1,
      title: "5个提升Instagram互动率的实用技巧",
      publishedTime: "2024-01-18 16:00",
      platforms: ["Instagram"],
      status: "published",
      actualReach: 15678,
      engagement: 8.5,
      likes: 1234,
      comments: 89,
      shares: 45
    },
    {
      id: 2,
      title: "TikTok算法解密：获得更多曝光的秘诀",
      publishedTime: "2024-01-17 19:30",
      platforms: ["TikTok"],
      status: "viral",
      actualReach: 32456,
      engagement: 12.3,
      likes: 2987,
      comments: 456,
      shares: 234
    },
    {
      id: 3,
      title: "LinkedIn个人品牌建设完整指南",
      publishedTime: "2024-01-16 08:00",
      platforms: ["LinkedIn"],
      status: "published",
      actualReach: 8234,
      engagement: 6.8,
      likes: 456,
      comments: 123,
      shares: 67
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'bg-green-100 text-green-800';
      case 'optimizing': return 'bg-yellow-100 text-yellow-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'published': return 'bg-blue-100 text-blue-800';
      case 'viral': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready': return <CheckCircle className="w-3 h-3" />;
      case 'optimizing': return <Sparkles className="w-3 h-3" />;
      case 'draft': return <Edit className="w-3 h-3" />;
      case 'published': return <CheckCircle className="w-3 h-3" />;
      case 'viral': return <BarChart3 className="w-3 h-3" />;
      default: return <AlertCircle className="w-3 h-3" />;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'Instagram': return <Instagram className="w-3 h-3" />;
      case 'TikTok': return <Youtube className="w-3 h-3" />;
      case 'LinkedIn': return <Linkedin className="w-3 h-3" />;
      case 'Twitter': return <Twitter className="w-3 h-3" />;
      default: return null;
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* 顶部标题栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">发布管理中心</h1>
          <p className="text-gray-600 mt-1">管理您的内容发布计划和历史记录</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <Calendar className="w-4 h-4 mr-2" />
            日历视图
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            创建内容
          </Button>
        </div>
      </div>

      {/* 内容管理 */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="scheduled">待发布 ({scheduledContent.length})</TabsTrigger>
          <TabsTrigger value="published">已发布 ({publishedContent.length})</TabsTrigger>
          <TabsTrigger value="drafts">草稿箱</TabsTrigger>
        </TabsList>

        <TabsContent value="scheduled" className="space-y-4">
          {scheduledContent.map((content) => (
            <Card key={content.id} className="border-0 shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{content.title}</h3>
                      <Badge className={`${getStatusColor(content.status)} text-xs`}>
                        {getStatusIcon(content.status)}
                        <span className="ml-1 capitalize">{content.status}</span>
                      </Badge>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{content.preview}</p>
                    
                    <div className="flex items-center space-x-6 text-sm text-gray-600 mb-3">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{content.scheduledTime}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Eye className="w-4 h-4" />
                        <span>预计覆盖: {content.estimatedReach.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Sparkles className="w-4 h-4" />
                        <span>AI评分: {content.aiScore}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">发布平台:</span>
                      {content.platforms.map((platform, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {getPlatformIcon(platform)}
                          <span className="ml-1">{platform}</span>
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <Button variant="outline" size="sm">
                      <Edit className="w-4 h-4 mr-1" />
                      编辑
                    </Button>
                    <Button variant="outline" size="sm">
                      <Play className="w-4 h-4 mr-1" />
                      立即发布
                    </Button>
                    <Button variant="outline" size="sm">
                      <Pause className="w-4 h-4 mr-1" />
                      暂停
                    </Button>
                    <Button variant="outline" size="sm">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="published" className="space-y-4">
          {publishedContent.map((content) => (
            <Card key={content.id} className="border-0 shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{content.title}</h3>
                      <Badge className={`${getStatusColor(content.status)} text-xs`}>
                        {getStatusIcon(content.status)}
                        <span className="ml-1 capitalize">
                          {content.status === 'viral' ? '🔥 爆款' : '已发布'}
                        </span>
                      </Badge>
                    </div>
                    
                    <div className="flex items-center space-x-6 text-sm text-gray-600 mb-3">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{content.publishedTime}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Eye className="w-4 h-4" />
                        <span>实际覆盖: {content.actualReach.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <BarChart3 className="w-4 h-4" />
                        <span>互动率: {content.engagement}%</span>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mb-3">
                      <div className="text-center p-2 bg-red-50 rounded">
                        <p className="text-lg font-bold text-red-600">{content.likes.toLocaleString()}</p>
                        <p className="text-xs text-gray-600">点赞</p>
                      </div>
                      <div className="text-center p-2 bg-blue-50 rounded">
                        <p className="text-lg font-bold text-blue-600">{content.comments}</p>
                        <p className="text-xs text-gray-600">评论</p>
                      </div>
                      <div className="text-center p-2 bg-green-50 rounded">
                        <p className="text-lg font-bold text-green-600">{content.shares}</p>
                        <p className="text-xs text-gray-600">分享</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">发布平台:</span>
                      {content.platforms.map((platform, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {getPlatformIcon(platform)}
                          <span className="ml-1">{platform}</span>
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-1" />
                      查看详情
                    </Button>
                    <Button variant="outline" size="sm">
                      <BarChart3 className="w-4 h-4 mr-1" />
                      分析
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="drafts" className="space-y-4">
          <Card className="border-0 shadow-sm">
            <CardContent className="p-12 text-center">
              <div className="text-gray-400 mb-4">
                <Edit className="w-12 h-12 mx-auto" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无草稿</h3>
              <p className="text-gray-600 mb-4">您还没有保存任何草稿内容</p>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                创建新内容
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
