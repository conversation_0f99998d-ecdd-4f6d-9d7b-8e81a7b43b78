'use client';

import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Hero } from "@/components/sections/Hero";
import { Features } from "@/components/sections/Features";
import { Stats } from "@/components/sections/Stats";
import { Pricing } from "@/components/sections/Pricing";
import { Testimonial } from "@/components/sections/Testimonial";
import { CTA } from "@/components/sections/CTA";
import { FAQ } from "@/components/sections/FAQ";
import { ContentFlowFooter } from "./Footer";

export const ContentFlowLandingPage = () => {
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1] || 'zh';

  // Get translations
  const tHero = useTranslations('hero');
  const tFeatures = useTranslations('features');
  const tStats = useTranslations('stats');
  const tPricing = useTranslations('pricing');

  const handleGetStarted = () => {
    router.push(`/${currentLocale}/auth/signin`);
  };

  const handleWatchDemo = () => {
    // TODO: 添加演示视频或产品演示的逻辑
    // 可以打开模态框显示产品演示视频或跳转到演示页面
  };

  // Hero section data from translations
  const heroData = {
    title: tHero('title'),
    subtitle: tHero('subtitle'),
    description: tHero('description'),
    cta: {
      primary: tHero('cta.primary'),
      secondary: tHero('cta.secondary')
    },
    trustIndicators: {
      users: tHero('trustIndicators.users'),
      content: tHero('trustIndicators.content'),
      uptime: tHero('trustIndicators.uptime')
    }
  };

  // Features section data from translations
  const featuresData = {
    title: tFeatures('title'),
    subtitle: tFeatures('subtitle'),
    items: [
      {
        title: tFeatures('items.0.title'),
        description: tFeatures('items.0.description'),
        icon: tFeatures('items.0.icon'),
        features: [
          tFeatures('items.0.features.0'),
          tFeatures('items.0.features.1'),
          tFeatures('items.0.features.2'),
          tFeatures('items.0.features.3')
        ]
      },
      {
        title: tFeatures('items.1.title'),
        description: tFeatures('items.1.description'),
        icon: tFeatures('items.1.icon'),
        features: [
          tFeatures('items.1.features.0'),
          tFeatures('items.1.features.1'),
          tFeatures('items.1.features.2'),
          tFeatures('items.1.features.3')
        ]
      },
      {
        title: tFeatures('items.2.title'),
        description: tFeatures('items.2.description'),
        icon: tFeatures('items.2.icon'),
        features: [
          tFeatures('items.2.features.0'),
          tFeatures('items.2.features.1'),
          tFeatures('items.2.features.2'),
          tFeatures('items.2.features.3')
        ]
      },
      {
        title: tFeatures('items.3.title'),
        description: tFeatures('items.3.description'),
        icon: tFeatures('items.3.icon'),
        features: [
          tFeatures('items.3.features.0'),
          tFeatures('items.3.features.1'),
          tFeatures('items.3.features.2'),
          tFeatures('items.3.features.3')
        ]
      },
      {
        title: tFeatures('items.4.title'),
        description: tFeatures('items.4.description'),
        icon: tFeatures('items.4.icon'),
        features: [
          tFeatures('items.4.features.0'),
          tFeatures('items.4.features.1'),
          tFeatures('items.4.features.2'),
          tFeatures('items.4.features.3')
        ]
      },
      {
        title: tFeatures('items.5.title'),
        description: tFeatures('items.5.description'),
        icon: tFeatures('items.5.icon'),
        features: [
          tFeatures('items.5.features.0'),
          tFeatures('items.5.features.1'),
          tFeatures('items.5.features.2'),
          tFeatures('items.5.features.3')
        ]
      }
    ]
  };

  // Stats section data from translations
  const statsData = {
    title: tStats('title'),
    subtitle: tStats('subtitle'),
    stats: [
      {
        value: tStats('stats.0.value'),
        label: tStats('stats.0.label'),
        description: tStats('stats.0.description')
      },
      {
        value: tStats('stats.1.value'),
        label: tStats('stats.1.label'),
        description: tStats('stats.1.description')
      },
      {
        value: tStats('stats.2.value'),
        label: tStats('stats.2.label'),
        description: tStats('stats.2.description')
      },
      {
        value: tStats('stats.3.value'),
        label: tStats('stats.3.label'),
        description: tStats('stats.3.description')
      }
    ]
  };

  // Pricing section data from translations
  const pricingData = {
    title: tPricing('title'),
    subtitle: tPricing('subtitle'),
    plans: [
      {
        name: tPricing('plans.0.name'),
        price: tPricing('plans.0.price'),
        period: tPricing('perMonth'),
        description: tPricing('plans.0.description'),
        features: [
          tPricing('plans.0.features.0'),
          tPricing('plans.0.features.1'),
          tPricing('plans.0.features.2'),
          tPricing('plans.0.features.3'),
          tPricing('plans.0.features.4')
        ],
        cta: tPricing('getStarted'),
        popular: false,
        amount: 0
      },
      {
        name: tPricing('plans.1.name'),
        price: tPricing('plans.1.price'),
        period: tPricing('perMonth'),
        description: tPricing('plans.1.description'),
        features: [
          tPricing('plans.1.features.0'),
          tPricing('plans.1.features.1'),
          tPricing('plans.1.features.2'),
          tPricing('plans.1.features.3'),
          tPricing('plans.1.features.4'),
          tPricing('plans.1.features.5')
        ],
        cta: tPricing('buyNow'),
        popular: true,
        amount: 29
      },
      {
        name: tPricing('plans.2.name'),
        price: tPricing('plans.2.price'),
        period: tPricing('perMonth'),
        description: tPricing('plans.2.description'),
        features: [
          tPricing('plans.2.features.0'),
          tPricing('plans.2.features.1'),
          tPricing('plans.2.features.2'),
          tPricing('plans.2.features.3'),
          tPricing('plans.2.features.4'),
          tPricing('plans.2.features.5'),
          tPricing('plans.2.features.6')
        ],
        cta: tPricing('contactUs'),
        popular: false,
        amount: 99
      },
      {
        name: tPricing('plans.3.name'),
        price: tPricing('plans.3.price'),
        period: tPricing('perMonth'),
        description: tPricing('plans.3.description'),
        features: [
          tPricing('plans.3.features.0'),
          tPricing('plans.3.features.1'),
          tPricing('plans.3.features.2'),
          tPricing('plans.3.features.3'),
          tPricing('plans.3.features.4'),
          tPricing('plans.3.features.5'),
          tPricing('plans.3.features.6')
        ],
        cta: tPricing('contactUs'),
        popular: false,
        amount: 299
      }
    ]
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white via-blue-50/20 to-indigo-50/10 dark:from-background dark:via-blue-950/5 dark:to-indigo-950/5">
      {/* Hero Section */}
      <Hero
        hero={heroData}
        onPrimaryClick={handleGetStarted}
        onSecondaryClick={handleWatchDemo}
      />

      {/* Elegant Section Divider */}
      <div className="relative py-8">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-blue-200/50 dark:border-blue-800/30"></div>
        </div>
        <div className="relative flex justify-center">
          <div className="bg-white dark:bg-background px-6">
            <div className="w-3 h-3 bg-gradient-to-r from-blue-300 to-indigo-300 rounded-full"></div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <Features features={featuresData} />

      {/* Elegant Section Divider */}
      <div className="relative py-12">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-blue-200/50 dark:border-blue-800/30"></div>
        </div>
        <div className="relative flex justify-center">
          <div className="bg-white dark:bg-background px-6">
            <div className="flex space-x-2">
              <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
              <div className="w-2 h-2 bg-indigo-300 rounded-full"></div>
              <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <Stats section={statsData} />

      {/* Elegant Section Divider */}
      <div className="relative py-12">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-blue-200/50 dark:border-blue-800/30"></div>
        </div>
        <div className="relative flex justify-center">
          <div className="bg-white dark:bg-background px-6">
            <div className="w-4 h-4 bg-gradient-to-r from-blue-300 via-indigo-300 to-blue-300 rounded-full"></div>
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <Pricing pricing={pricingData} />

      {/* Testimonials Section */}
      <Testimonial section={{
        title: "用户怎么说",
        subtitle: "听听我们用户的真实反馈",
        testimonials: [
          {
            content: "ContentFlow彻底改变了我的内容创作流程。AI生成的内容质量很高，多平台发布功能节省了我大量时间。",
            author: {
              name: "张小明",
              title: "自媒体博主",
              company: "个人工作室",
              image: "/avatars/user1.jpg"
            }
          },
          {
            content: "作为营销团队，我们需要管理多个品牌的社交媒体。ContentFlow的团队协作功能让我们的工作效率提升了80%。",
            author: {
              name: "李经理",
              title: "市场营销总监",
              company: "科技公司",
              image: "/avatars/user2.jpg"
            }
          },
          {
            content: "数据分析功能非常强大，帮助我们优化内容策略，粉丝增长速度比以前快了3倍。",
            author: {
              name: "王创业",
              title: "电商运营",
              company: "电商企业",
              image: "/avatars/user3.jpg"
            }
          }
        ]
      }} />

      {/* CTA Section */}
      <CTA
        section={{
          title: "准备开始您的内容创作之旅？",
          subtitle: "加入数万创作者的行列，让AI助力您的内容创作",
          cta: {
            primary: "免费开始使用",
            secondary: "预约演示"
          }
        }}
        onPrimaryClick={handleGetStarted}
        onSecondaryClick={handleWatchDemo}
      />

      {/* FAQ Section */}
      <FAQ section={{
        title: "常见问题",
        subtitle: "解答您关于ContentFlow的疑问",
        faqs: [
          {
            question: "ContentFlow支持哪些社交媒体平台？",
            answer: "我们支持主流的社交媒体平台，包括微信公众号、微博、抖音、小红书、Instagram、Facebook、Twitter、LinkedIn、TikTok等。我们会持续添加更多平台支持。"
          },
          {
            question: "AI生成的内容质量如何？",
            answer: "我们使用最新的GPT-4模型，结合您的品牌调性和历史数据进行训练，生成的内容质量很高。同时支持人工编辑和优化，确保内容符合您的要求。"
          },
          {
            question: "是否支持团队协作？",
            answer: "是的，我们提供完整的团队协作功能，包括角色权限管理、内容审核流程、评论协作等，让团队成员可以高效协作。"
          },
          {
            question: "数据安全如何保障？",
            answer: "我们采用企业级安全标准，所有数据都经过加密存储和传输。我们通过了SOC 2认证，严格遵守数据保护法规。"
          },
          {
            question: "可以免费试用吗？",
            answer: "是的，我们提供14天免费试用，无需信用卡。试用期内您可以体验所有核心功能。"
          }
        ]
      }} />

      {/* Footer */}
      <ContentFlowFooter />
    </div>
  );
};
