'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import { 
  PenTool, 
  Upload,
  Sparkles,
  Eye,
  Save,
  Send,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  Calendar,
  Clock,
  Hash,
  Image as ImageIcon,
  Video,
  Type,
  Lightbulb,
  Target,
  TrendingUp,
  Zap,
  CheckCircle,
  AlertCircle,
  BarChart3,
  MessageCircle,
  Heart,
  Share2,
  Wand2
} from 'lucide-react';

export const ContentFlowEditor = () => {
  const [content, setContent] = useState({
    title: '',
    body: '',
    hashtags: '',
    platforms: {
      instagram: true,
      tiktok: false,
      linkedin: false,
      twitter: false
    },
    publishType: 'now',
    scheduledTime: ''
  });

  const [aiAnalysis, setAiAnalysis] = useState({
    overallScore: 8.5,
    simplicity: 8.2,
    specificity: 7.8,
    emotion: 9.1,
    hookQuality: 8.7,
    predictions: {
      estimatedReach: 15600,
      estimatedEngagement: 1340,
      viralPotential: 'high'
    }
  });

  const [aiSuggestions, setAiSuggestions] = useState([
    { 
      type: 'title', 
      priority: 'high',
      text: '建议在标题中加入数字，如"5个技巧"可提升点击率38%',
      impact: '+38% 点击率'
    },
    { 
      type: 'hashtag', 
      priority: 'medium',
      text: '推荐热门标签：#内容营销 #社交媒体 #创作技巧',
      impact: '+25% 覆盖率'
    },
    { 
      type: 'timing', 
      priority: 'high',
      text: '最佳发布时间：今天下午3-5点（用户活跃度峰值）',
      impact: '+42% 互动率'
    },
    { 
      type: 'engagement', 
      priority: 'medium',
      text: '在内容末尾添加问题可以提高评论互动率56%',
      impact: '+56% 评论数'
    }
  ]);

  const platforms = [
    { 
      id: 'instagram', 
      name: 'Instagram', 
      icon: Instagram, 
      color: 'bg-pink-500', 
      active: content.platforms.instagram,
      optimalLength: '2200字符',
      bestTime: '15:00-17:00',
      estimatedReach: '12.5K'
    },
    { 
      id: 'tiktok', 
      name: 'TikTok', 
      icon: Youtube, 
      color: 'bg-red-500', 
      active: content.platforms.tiktok,
      optimalLength: '150字符',
      bestTime: '19:00-21:00',
      estimatedReach: '8.7K'
    },
    { 
      id: 'linkedin', 
      name: 'LinkedIn', 
      icon: Linkedin, 
      color: 'bg-blue-600', 
      active: content.platforms.linkedin,
      optimalLength: '1300字符',
      bestTime: '08:00-10:00',
      estimatedReach: '3.2K'
    },
    { 
      id: 'twitter', 
      name: 'Twitter', 
      icon: Twitter, 
      color: 'bg-blue-400', 
      active: content.platforms.twitter,
      optimalLength: '280字符',
      bestTime: '12:00-15:00',
      estimatedReach: '5.1K'
    }
  ];

  const handlePlatformToggle = (platformId: string) => {
    setContent(prev => ({
      ...prev,
      platforms: {
        ...prev.platforms,
        [platformId]: !prev.platforms[platformId as keyof typeof prev.platforms]
      }
    }));
  };

  const handleAiOptimize = () => {
    toast.success("AI优化完成，内容已根据最佳实践进行优化");
  };

  const handlePublish = () => {
    if (!content.title || !content.body) {
      toast.error("请完善内容，标题和正文内容不能为空");
      return;
    }

    const selectedPlatforms = Object.entries(content.platforms)
      .filter(([_, active]) => active)
      .map(([platform, _]) => platform);

    if (selectedPlatforms.length === 0) {
      toast.error("请选择发布平台，至少选择一个社交媒体平台");
      return;
    }

    toast.success(`内容发布成功，已发布到 ${selectedPlatforms.length} 个平台`);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* 顶部标题栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">内容创作工作台</h1>
          <p className="text-gray-600 mt-1">AI驱动的智能内容创作与优化</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={handleAiOptimize}>
            <Wand2 className="w-4 h-4 mr-2" />
            AI优化
          </Button>
          <Button variant="outline">
            <Save className="w-4 h-4 mr-2" />
            保存草稿
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700" onClick={handlePublish}>
            <Send className="w-4 h-4 mr-2" />
            发布内容
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 主要编辑区域 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 内容编辑器 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <PenTool className="w-5 h-5 mr-2" />
                内容编辑器
              </CardTitle>
              <CardDescription>创作引人入胜的内容</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">标题</Label>
                <Input
                  id="title"
                  placeholder="输入吸引人的标题..."
                  value={content.title}
                  onChange={(e) => setContent({...content, title: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="body">正文内容</Label>
                <Textarea
                  id="body"
                  placeholder="分享您的想法和见解..."
                  className="min-h-[200px]"
                  value={content.body}
                  onChange={(e) => setContent({...content, body: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="hashtags">标签</Label>
                <Input
                  id="hashtags"
                  placeholder="#内容营销 #社交媒体 #创作技巧"
                  value={content.hashtags}
                  onChange={(e) => setContent({...content, hashtags: e.target.value})}
                />
              </div>
            </CardContent>
          </Card>

          {/* 平台选择 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Target className="w-5 h-5 mr-2" />
                发布平台
              </CardTitle>
              <CardDescription>选择要发布的社交媒体平台</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {platforms.map((platform) => (
                  <div key={platform.id} className="p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${platform.color} text-white`}>
                          <platform.icon className="w-4 h-4" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900">{platform.name}</h4>
                          <p className="text-sm text-gray-600">预计覆盖: {platform.estimatedReach}</p>
                        </div>
                      </div>
                      <Switch
                        checked={platform.active}
                        onCheckedChange={() => handlePlatformToggle(platform.id)}
                      />
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>最佳长度: {platform.optimalLength}</p>
                      <p>最佳时间: {platform.bestTime}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧边栏 - AI分析 */}
        <div className="space-y-6">
          {/* AI内容分析 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Sparkles className="w-5 h-5 mr-2 text-purple-500" />
                AI内容分析
              </CardTitle>
              <CardDescription>实时分析内容质量和表现预测</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">{aiAnalysis.overallScore}</div>
                <p className="text-sm text-gray-600">综合评分</p>
              </div>

              <div className="space-y-3">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-600">简洁性</span>
                    <span className="text-sm font-medium">{aiAnalysis.simplicity}</span>
                  </div>
                  <Progress value={aiAnalysis.simplicity * 10} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-600">具体性</span>
                    <span className="text-sm font-medium">{aiAnalysis.specificity}</span>
                  </div>
                  <Progress value={aiAnalysis.specificity * 10} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-600">情感共鸣</span>
                    <span className="text-sm font-medium">{aiAnalysis.emotion}</span>
                  </div>
                  <Progress value={aiAnalysis.emotion * 10} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-600">吸引力</span>
                    <span className="text-sm font-medium">{aiAnalysis.hookQuality}</span>
                  </div>
                  <Progress value={aiAnalysis.hookQuality * 10} className="h-2" />
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900">表现预测</h4>
                <div className="grid grid-cols-2 gap-3">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-center mb-1">
                      <Eye className="w-4 h-4 text-blue-600 mr-1" />
                      <span className="text-sm font-medium text-blue-900">预计覆盖</span>
                    </div>
                    <p className="text-lg font-bold text-blue-600">{aiAnalysis.predictions.estimatedReach.toLocaleString()}</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center justify-center mb-1">
                      <Heart className="w-4 h-4 text-green-600 mr-1" />
                      <span className="text-sm font-medium text-green-900">预计互动</span>
                    </div>
                    <p className="text-lg font-bold text-green-600">{aiAnalysis.predictions.estimatedEngagement.toLocaleString()}</p>
                  </div>
                </div>
                <div className="text-center p-3 bg-orange-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <TrendingUp className="w-4 h-4 text-orange-600 mr-1" />
                    <span className="text-sm font-medium text-orange-900">病毒传播潜力</span>
                  </div>
                  <Badge className="bg-orange-100 text-orange-800 capitalize">
                    {aiAnalysis.predictions.viralPotential}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* AI优化建议 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="w-5 h-5 mr-2 text-yellow-500" />
                AI优化建议
              </CardTitle>
              <CardDescription>基于数据分析的改进建议</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {aiSuggestions.map((suggestion, index) => (
                <div key={index} className={`p-3 rounded-lg border ${getPriorityColor(suggestion.priority)}`}>
                  <div className="flex items-start justify-between mb-2">
                    <Badge variant="outline" className="text-xs capitalize">
                      {suggestion.priority} 优先级
                    </Badge>
                    <span className="text-xs font-medium">{suggestion.impact}</span>
                  </div>
                  <p className="text-sm">{suggestion.text}</p>
                </div>
              ))}
              <Button variant="outline" size="sm" className="w-full">
                应用所有建议
              </Button>
            </CardContent>
          </Card>

          {/* 发布设置 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                发布设置
              </CardTitle>
              <CardDescription>选择发布时间和方式</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="now"
                    name="publishType"
                    value="now"
                    checked={content.publishType === 'now'}
                    onChange={(e) => setContent({...content, publishType: e.target.value})}
                  />
                  <Label htmlFor="now" className="flex items-center">
                    <Send className="w-4 h-4 mr-2" />
                    立即发布
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="schedule"
                    name="publishType"
                    value="schedule"
                    checked={content.publishType === 'schedule'}
                    onChange={(e) => setContent({...content, publishType: e.target.value})}
                  />
                  <Label htmlFor="schedule" className="flex items-center">
                    <Clock className="w-4 h-4 mr-2" />
                    定时发布
                  </Label>
                </div>
              </div>

              {content.publishType === 'schedule' && (
                <div className="space-y-2">
                  <Label htmlFor="scheduledTime">发布时间</Label>
                  <Input
                    id="scheduledTime"
                    type="datetime-local"
                    value={content.scheduledTime}
                    onChange={(e) => setContent({...content, scheduledTime: e.target.value})}
                  />
                </div>
              )}

              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Zap className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">最佳发布时间</span>
                </div>
                <p className="text-xs text-blue-700">
                  根据您的受众分析，建议在今天下午3-5点发布，预计可提升42%的互动率
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
