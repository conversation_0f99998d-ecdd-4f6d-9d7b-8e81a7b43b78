'use client';

import { usePathname } from 'next/navigation';
import Header from './Header';

interface ConditionalHeaderProps {
  header: any;
}

export function ConditionalHeader({ header }: ConditionalHeaderProps) {
  const pathname = usePathname();

  // Don't show marketing header on app routes - they have their own header
  if (pathname.includes('/app/')) {
    return null;
  }

  // Don't show header on auth pages for cleaner login experience
  if (pathname.includes('/auth/')) {
    return null;
  }

  // Show marketing header on public pages (landing, pricing, etc.)
  return <Header header={header} />;
}

// Component to wrap public page content with proper padding
export function PublicPageWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // App routes handle their own layout
  if (pathname.includes('/app/')) {
    return <>{children}</>;
  }

  // Auth pages don't need header padding
  if (pathname.includes('/auth/')) {
    return <>{children}</>;
  }

  // Public pages need top padding for fixed header
  return (
    <div className="pt-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {children}
      </div>
    </div>
  );
}
