'use client';

import { usePathname } from 'next/navigation';
import Header from './Header';

interface ConditionalHeaderProps {
  header: any;
}

export function ConditionalHeader({ header }: ConditionalHeaderProps) {
  const pathname = usePathname();

  // Don't show marketing header on app routes - they have their own header
  if (pathname.includes('/app/')) {
    return null;
  }

  // Don't show header on auth pages for cleaner login experience
  if (pathname.includes('/auth/')) {
    return null;
  }

  // Show marketing header on public pages (landing, pricing, etc.)
  return <Header header={header} />;
}

// Component to wrap public page content with proper padding
export function PublicPageWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // App routes handle their own layout
  if (pathname.includes('/app/')) {
    return <>{children}</>;
  }

  // Auth pages don't need header padding
  if (pathname.includes('/auth/')) {
    return <>{children}</>;
  }

  // Public pages need top padding for fixed header with responsive design
  return (
    <div className="pt-16 min-h-screen">
      {/* Container with responsive max-width for different screen sizes */}
      <div className="w-full">
        {/* Full-width sections for hero and other landing page components */}
        <div className="w-full">
          {children}
        </div>
      </div>
    </div>
  );
}
