"use client";

import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";

interface CTAProps {
  section: {
    title: string;
    subtitle: string;
    cta: {
      primary: string;
      secondary: string;
    };
  };
  onPrimaryClick?: () => void;
  onSecondaryClick?: () => void;
}

export function CTA({ section, onPrimaryClick, onSecondaryClick }: CTAProps) {
  return (
    <section id="cta" className="py-32 relative overflow-hidden bg-gradient-to-b from-rose-50/50 to-pink-50/30 dark:from-rose-950/20 dark:to-pink-950/10">
      {/* Wedding-themed background gradient */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-b from-background/50 via-rose-50/30 to-pink-50/20 dark:from-background/50 dark:via-rose-950/20 dark:to-pink-950/10" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_800px_at_50%_400px,rgba(244,63,94,0.1),transparent)] dark:bg-[radial-gradient(circle_800px_at_50%_400px,rgba(244,63,94,0.15),transparent)]" />
      </div>

      {/* Elegant grid background */}
      <div className="absolute inset-0 -z-10">
        <div className="h-full w-full bg-[linear-gradient(to_right,rgba(244,63,94,0.05)_1px,transparent_1px),linear-gradient(to_bottom,rgba(244,63,94,0.05)_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,rgba(251,113,133,0.08)_1px,transparent_1px),linear-gradient(to_bottom,rgba(251,113,133,0.08)_1px,transparent_1px)] bg-[size:50px_50px]" />
      </div>

      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="max-w-5xl mx-auto text-center"
        >
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 wedding-text-gradient">
            {section.title}
          </h2>
          <p className="text-xl md:text-2xl text-rose-700 dark:text-rose-200 mb-12 max-w-3xl mx-auto leading-relaxed">
            {section.subtitle}
          </p>
          <div className="flex flex-col sm:flex-row gap-8 justify-center">
            <Button
              size="lg"
              onClick={onPrimaryClick}
              className="wedding-button-elegant rounded-full px-12 h-16 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              {section.cta.primary}
            </Button>
            <Button
              size="lg"
              variant="outline"
              onClick={onSecondaryClick}
              className="rounded-full px-12 h-16 text-lg font-semibold border-2 border-rose-300 text-rose-700 hover:bg-rose-50 dark:border-rose-600 dark:text-rose-300 dark:hover:bg-rose-950/30 transition-all duration-300 transform hover:scale-105"
            >
              {section.cta.secondary}
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
