"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { Check, Star, Zap, Crown, Building, Rocket, Sparkles } from "lucide-react";
import { loadStripe } from '@stripe/stripe-js';
import { toast } from "sonner";
import { useRouter, usePathname } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY!);

interface Plan {
  name: string;
  price: string;
  amount: number;
  description: string;
  features: string[];
  popular?: boolean;
  cta: string;
  period?: string;
}

interface PricingProps {
  pricing: {
    title: string;
    subtitle: string;
    plans: Plan[];
  };
}

const planIcons = {
  free: Z<PERSON>,
  creator: <PERSON>,
  team: Crown,
  agency: Building
};

export function Pricing({ pricing }: PricingProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const pathname = usePathname();
  const locale = pathname.split('/')[1] || 'zh';
  const t = useTranslations('pricing');

  const handlePayment = async (price: number, productName?: string) => {
    if (!session) {
      toast.error(t('errors.loginRequired'));
      router.push(`/${locale}/auth/signin`);
      return;
    }

    try {
      const response = await fetch("/api/stripe", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          price,
          email: session.user?.email,
          productName: productName || 'ContentFlow Subscription',
          successUrl: `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/orders?session_id={CHECKOUT_SESSION_ID}&amount=${price}`,
          cancelUrl: `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/#pricing`,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Payment request failed');
      }

      const { url } = await response.json();
      if (url) {
        window.location.href = url;
      } else {
        throw new Error('No checkout URL received');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "支付失败，请重试");
      console.error("Payment error:", error);
    }
  };

  const getPlanIcon = (index: number) => {
    const icons = [Zap, Star, Crown, Building];
    return icons[index] || Rocket;
  };

  return (
    <section id="pricing" className="py-24 bg-gradient-to-b from-white to-gray-50/50 dark:from-background dark:to-gray-900/50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(99,102,241,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(99,102,241,0.1),transparent_50%)]"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Badge className="mb-6 bg-blue-100 text-blue-800 hover:bg-blue-100 border-blue-200 dark:bg-blue-950/50 dark:text-blue-300 dark:border-blue-800 shadow-sm">
              <Sparkles className="w-4 h-4 mr-1" />
              定价方案
            </Badge>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 dark:from-blue-400 dark:via-indigo-400 dark:to-purple-400">
              {pricing.title}
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              {pricing.subtitle}
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {pricing.plans.map((plan, index) => {
            const IconComponent = getPlanIcon(index);
            const isPopular = plan.popular;

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`relative ${isPopular ? 'lg:scale-105' : ''}`}
              >
                {isPopular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <Badge className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0 shadow-lg">
                      <Star className="w-4 h-4 mr-1" />
                      最受欢迎
                    </Badge>
                  </div>
                )}

                <Card className={`h-full p-8 transition-all duration-300 hover:shadow-xl ${
                  isPopular
                    ? 'border-blue-200 dark:border-blue-800 bg-gradient-to-b from-blue-50/50 to-white dark:from-blue-950/20 dark:to-gray-800'
                    : 'hover:scale-105 bg-white/80 dark:bg-gray-800/80'
                } backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50`}>
                  <div className="flex flex-col h-full">
                    {/* Icon and Plan Name */}
                    <div className="text-center mb-6">
                      <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center ${
                        isPopular
                          ? 'bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/50 dark:to-indigo-900/50'
                          : 'bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700'
                      }`}>
                        <IconComponent className={`w-8 h-8 ${
                          isPopular ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'
                        }`} />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        {plan.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {plan.description}
                      </p>
                    </div>

                    {/* Price */}
                    <div className="text-center mb-8">
                      <div className="flex items-baseline justify-center">
                        <span className="text-4xl font-bold text-gray-900 dark:text-gray-100">
                          {plan.price}
                        </span>
                        {plan.period && (
                          <span className="text-gray-500 dark:text-gray-400 ml-1">
                            {plan.period}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Features */}
                    <div className="flex-1 mb-8">
                      <ul className="space-y-3">
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start gap-3">
                            <Check className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                            <span className="text-sm text-gray-600 dark:text-gray-300">
                              {feature}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* CTA Button */}
                    <Button
                      className={`w-full ${
                        isPopular
                          ? 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-0'
                          : plan.amount === 0
                          ? 'bg-gray-100 hover:bg-gray-200 text-gray-900 border border-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-100 dark:border-gray-600'
                          : 'border-2 border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-950/30'
                      } transition-all duration-300`}
                      onClick={() => {
                        if (plan.amount > 0) {
                          handlePayment(plan.amount, plan.name);
                        } else if (plan.amount === 0) {
                          router.push(`/${locale}/auth/signin`);
                        } else {
                          // Contact sales
                          window.location.href = '#contact';
                        }
                      }}
                    >
                      {plan.cta}
                    </Button>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            所有方案都包含14天免费试用，随时可以取消
          </p>
          <div className="flex items-center justify-center gap-6 text-sm text-gray-400 dark:text-gray-500">
            <span className="flex items-center gap-1">
              <Check className="w-4 h-4 text-green-500" />
              无设置费用
            </span>
            <span className="flex items-center gap-1">
              <Check className="w-4 h-4 text-green-500" />
              24/7客户支持
            </span>
            <span className="flex items-center gap-1">
              <Check className="w-4 h-4 text-green-500" />
              安全支付
            </span>
          </div>
        </motion.div>
      </div>
    </section>
  );
}