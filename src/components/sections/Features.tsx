"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { useTranslations } from 'next-intl';
import {
  Zap,
  Globe,
  BarChart3,
  Calendar,
  TrendingUp,
  Users,
  Brain,
  Target,
  Sparkles,
  ArrowRight
} from "lucide-react";

interface Feature {
  title: string;
  description: string;
  icon: string;
  features: string[];
}

interface FeaturesProps {
  features: {
    title: string;
    subtitle: string;
    items: Feature[];
  };
}

const iconMap = {
  ai: Brain,
  platforms: Globe,
  analytics: BarChart3,
  schedule: Calendar,
  prediction: TrendingUp,
  team: Users,
  speed: Zap,
  shield: Target,
};

export function Features({ features }: FeaturesProps) {
  const t = useTranslations('features');

  return (
    <section id="features" className="section-padding bg-gradient-to-b from-gray-50/50 to-white dark:from-gray-900/50 dark:to-background relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(99,102,241,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(99,102,241,0.1),transparent_50%)]"></div>

      <div className="container-responsive relative z-10">
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Badge className="mb-6 bg-blue-100 text-blue-800 hover:bg-blue-100 border-blue-200 dark:bg-blue-950/50 dark:text-blue-300 dark:border-blue-800 shadow-sm">
              <Sparkles className="w-4 h-4 mr-1" />
              {t('badge')}
            </Badge>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 dark:from-blue-400 dark:via-indigo-400 dark:to-purple-400">
              {features.title}
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              {features.subtitle}
            </p>
          </motion.div>
        </div>

        <div className="grid-responsive-3 gap-6 md:gap-8">
          {features.items.map((feature, index) => {
            const IconComponent = iconMap[feature.icon as keyof typeof iconMap] || Zap;
            
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="group h-full p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50">
                  <div className="flex flex-col h-full">
                    {/* Icon */}
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/50 dark:to-indigo-900/50 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                      <IconComponent className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                        {feature.description}
                      </p>

                      {/* Feature list */}
                      <div className="space-y-2">
                        {feature.features.map((item, itemIndex) => (
                          <div key={itemIndex} className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                            <span>{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Learn more link */}
                    <div className="mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50">
                      <button className="group/link flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors text-sm font-medium">
                        了解更多
                        <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
                      </button>
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">{t('cta.title')}</h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              {t('cta.description')}
            </p>
            <button className="bg-white text-blue-600 hover:bg-gray-50 px-8 py-3 rounded-full font-semibold transition-colors">
              {t('cta.button')}
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
