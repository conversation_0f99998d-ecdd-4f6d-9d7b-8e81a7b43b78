"use client";

import { motion } from "framer-motion";
import { Star } from "lucide-react";

interface TestimonialProps {
  section: {
    title: string;
    subtitle: string;
    testimonials: Array<{
      content: string;
      author: {
        name: string;
        title: string;
        company: string;
        image: string;
      };
    }>;
  };
}

import { TestimonialsSection } from "@/components/blocks/testimonials-with-marquee";

export function Testimonial({ section }: TestimonialProps) {
  // Convert section testimonials to the format expected by TestimonialsSection
  const testimonials = section.testimonials.map((testimonial) => ({
    author: {
      name: testimonial.author.name,
      handle: `@${testimonial.author.name.toLowerCase().replace(/\s+/g, '')}`,
      avatar: testimonial.author.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(testimonial.author.name)}&background=1890FF&color=fff`
    },
    text: testimonial.content,
    href: "#"
  }));

  return (
    <section id="testimonials" className="py-24 bg-gradient-to-b from-rose-50/30 to-pink-50/20 dark:from-rose-950/10 dark:to-pink-950/5 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_50%,rgba(244,63,94,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_20%_50%,rgba(244,63,94,0.1),transparent_50%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(236,72,153,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_80%_20%,rgba(236,72,153,0.1),transparent_50%)]"></div>

      <div className="relative z-10">
        <TestimonialsSection
          title={section.title}
          description={section.subtitle}
          testimonials={testimonials}
        />
      </div>
    </section>
  );
}