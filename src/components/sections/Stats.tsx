"use client";

interface StatsProps {
  section: {
    title: string;
    subtitle: string;
    stats: Array<{
      value: string;
      label: string;
      description: string;
    }>;
  };
}

export function Stats({ section }: StatsProps) {
  return (
    <section id="stats" className="py-24 bg-gradient-to-b from-rose-50/50 to-pink-50/30 dark:from-rose-950/20 dark:to-pink-950/10">
      <div className="container mx-auto px-4">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 wedding-text-gradient">{section.title}</h2>
          <p className="text-xl text-rose-700 dark:text-rose-200 max-w-3xl mx-auto leading-relaxed">{section.subtitle}</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-10">
          {section.stats.map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="wedding-card p-8 hover:shadow-lg transition-all duration-300 hover:scale-105">
                <div className="text-5xl font-bold text-rose-600 dark:text-rose-400 mb-4 group-hover:scale-110 transition-transform duration-300">{stat.value}</div>
                <div className="text-xl font-semibold mb-3 text-rose-900 dark:text-rose-100">{stat.label}</div>
                <p className="text-rose-700 dark:text-rose-200 leading-relaxed">{stat.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
