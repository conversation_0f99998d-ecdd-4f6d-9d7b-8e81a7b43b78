"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { <PERSON>rk<PERSON>, Zap, TrendingUp, Play } from "lucide-react";

interface HeroProps {
  hero: {
    title: string;
    subtitle: string;
    description: string;
    cta: {
      primary: string;
      secondary: string;
    };
    trustIndicators?: {
      users: string;
      content: string;
      uptime: string;
    };
  };
  onPrimaryClick?: () => void;
  onSecondaryClick?: () => void;
}

export function Hero({ hero, onPrimaryClick, onSecondaryClick }: HeroProps) {
  return (
    <section id="hero" className="relative flex items-center justify-center min-h-[calc(100vh-4rem)] w-full py-12 md:py-24 lg:py-32 overflow-hidden">
      {/* Professional AI-themed Background */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-background via-blue-50/30 to-indigo-50/20 dark:from-background dark:via-blue-950/20 dark:to-indigo-950/10" />
        {/* AI-themed radial gradients */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_800px_at_50%_200px,rgba(59,130,246,0.08),transparent)] dark:bg-[radial-gradient(circle_800px_at_50%_200px,rgba(59,130,246,0.15),transparent)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_400px_at_80%_100px,rgba(99,102,241,0.06),transparent)] dark:bg-[radial-gradient(circle_400px_at_80%_100px,rgba(99,102,241,0.12),transparent)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_300px_at_20%_300px,rgba(147,51,234,0.05),transparent)] dark:bg-[radial-gradient(circle_300px_at_20%_300px,rgba(147,51,234,0.1),transparent)]" />
      </div>

      {/* Professional grid background */}
      <div className="absolute inset-0 -z-10">
        <div className="h-full w-full bg-[linear-gradient(to_right,rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(to_bottom,rgba(59,130,246,0.03)_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,rgba(99,102,241,0.05)_1px,transparent_1px),linear-gradient(to_bottom,rgba(99,102,241,0.05)_1px,transparent_1px)] bg-[size:60px_60px]" />
      </div>

      {/* AI-themed floating elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-300/30 dark:bg-blue-400/50 rounded-full animate-pulse" style={{ animationDelay: '0s', animationDuration: '3s' }} />
        <div className="absolute top-1/3 right-1/4 w-1 h-1 bg-indigo-300/35 dark:bg-indigo-400/60 rounded-full animate-pulse" style={{ animationDelay: '1s', animationDuration: '4s' }} />
        <div className="absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-purple-200/25 dark:bg-purple-300/45 rounded-full animate-pulse" style={{ animationDelay: '2s', animationDuration: '5s' }} />
        <div className="absolute top-2/3 right-1/3 w-1 h-1 bg-blue-200/30 dark:bg-blue-300/50 rounded-full animate-pulse" style={{ animationDelay: '1.5s', animationDuration: '3.5s' }} />
      </div>

      <div className="container px-4 md:px-6 mx-auto">
        <div className="flex flex-col items-center justify-center space-y-10 text-center">
          {/* AI Badge */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Badge className="mb-6 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 hover:from-blue-200 hover:to-indigo-200 border-blue-200 dark:from-blue-950/50 dark:to-indigo-950/50 dark:text-blue-300 dark:border-blue-800 shadow-sm px-4 py-2">
              <Sparkles className="w-4 h-4 mr-2" />
              AI驱动的内容创作平台
            </Badge>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="space-y-6 max-w-5xl mx-auto"
          >
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 dark:from-blue-400 dark:via-indigo-400 dark:to-purple-400 leading-tight">
              {hero.title}
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl text-gray-700 dark:text-gray-200 max-w-4xl mx-auto font-medium leading-relaxed">
              {hero.subtitle}
            </p>
            <p className="text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              {hero.description}
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            className="flex flex-col sm:flex-row items-center justify-center gap-6"
          >
            <Button
              size="lg"
              onClick={onPrimaryClick}
              className="group relative overflow-hidden bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-full px-8 sm:px-12 py-4 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-0"
            >
              <span className="relative z-10 flex items-center gap-2">
                <Zap className="w-5 h-5" />
                {hero.cta.primary}
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={onSecondaryClick}
              className="group rounded-full px-8 sm:px-12 py-4 font-semibold border-2 border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-950/30 transition-all duration-300 transform hover:scale-105"
            >
              <span className="flex items-center gap-2">
                <Play className="w-5 h-5" />
                {hero.cta.secondary}
              </span>
            </Button>
          </motion.div>

          {/* Enhanced Trust indicators */}
          {hero.trustIndicators && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-12 max-w-2xl mx-auto"
            >
              <div className="flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-300">{hero.trustIndicators.users}</span>
                </div>
              </div>
              <div className="flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-300">{hero.trustIndicators.content}</span>
                </div>
              </div>
              <div className="flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-300">{hero.trustIndicators.uptime}</span>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </section>
  );
}
