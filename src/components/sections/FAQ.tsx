"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface FAQProps {
  section: {
    title: string;
    subtitle: string;
    faqs: Array<{
      question: string;
      answer: string;
    }>;
  };
}

export function FAQ({ section }: FAQProps) {
  return (
    <section id="faq" className="py-24 bg-gradient-to-b from-rose-50/30 to-pink-50/20 dark:from-rose-950/10 dark:to-pink-950/5 relative overflow-hidden">
      {/* Wedding-themed background decoration */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_600px_at_50%_50%,rgba(244,63,94,0.05),transparent)] dark:bg-[radial-gradient(circle_600px_at_50%_50%,rgba(244,63,94,0.1),transparent)]" />
        <div className="h-full w-full bg-[linear-gradient(to_right,rgba(244,63,94,0.03)_1px,transparent_1px),linear-gradient(to_bottom,rgba(244,63,94,0.03)_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,rgba(251,113,133,0.05)_1px,transparent_1px),linear-gradient(to_bottom,rgba(251,113,133,0.05)_1px,transparent_1px)] bg-[size:50px_50px]" />
      </div>

      <div className="container mx-auto px-4">
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 wedding-text-gradient">{section.title}</h2>
          <p className="text-xl text-rose-700 dark:text-rose-200 max-w-3xl mx-auto leading-relaxed">{section.subtitle}</p>
        </div>
        <div className="max-w-4xl mx-auto">
          <div className="wedding-card backdrop-blur-sm rounded-2xl shadow-xl p-10">
            <Accordion type="single" collapsible className="space-y-6">
              {section.faqs.map((faq, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="border border-rose-200 dark:border-rose-700 rounded-xl px-6 py-3 bg-white/70 dark:bg-rose-950/30 backdrop-blur-sm hover:bg-white/90 dark:hover:bg-rose-950/50 transition-all duration-300 hover:shadow-md"
                >
                  <AccordionTrigger className="text-left text-rose-900 dark:text-rose-100 font-semibold hover:text-rose-600 dark:hover:text-rose-300 transition-colors">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-rose-700 dark:text-rose-200 leading-relaxed pt-3">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      </div>
    </section>
  );
}
