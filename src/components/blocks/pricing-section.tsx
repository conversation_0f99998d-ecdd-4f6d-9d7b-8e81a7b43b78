"use client"

import * as React from "react"
import { PricingCard, type PricingTier } from "@/components/ui/pricing-card"
import { Tab } from "@/components/ui/pricing-tab"

interface PricingSectionProps {
  title: string
  subtitle: string
  tiers: PricingTier[]
  frequencies: string[]
}

export function PricingSection({
  title,
  subtitle,
  tiers,
  frequencies,
}: PricingSectionProps) {
  const [selectedFrequency, setSelectedFrequency] = React.useState(frequencies[0])

  return (
    <section className="flex flex-col items-center gap-12 py-16 bg-gradient-to-b from-rose-50/30 to-pink-50/20 dark:from-rose-950/10 dark:to-pink-950/5">
      <div className="space-y-8 text-center">
        <div className="space-y-6">
          <h1 className="text-4xl font-bold md:text-5xl wedding-text-gradient">{title}</h1>
          <p className="text-xl text-rose-700 dark:text-rose-200 max-w-2xl mx-auto leading-relaxed">{subtitle}</p>
        </div>
        <div className="mx-auto flex w-fit rounded-full bg-rose-100/50 dark:bg-rose-950/30 p-1 border border-rose-200 dark:border-rose-800">
          {frequencies.map((freq) => (
            <Tab
              key={freq}
              text={freq}
              selected={selectedFrequency === freq}
              setSelected={setSelectedFrequency}
              discount={freq === "yearly"}
            />
          ))}
        </div>
      </div>

      <div className="grid w-full max-w-6xl gap-8 sm:grid-cols-2 xl:grid-cols-4">
        {tiers.map((tier) => (
          <PricingCard
            key={tier.name}
            tier={tier}
            paymentFrequency={selectedFrequency}
          />
        ))}
      </div>
    </section>
  )
}