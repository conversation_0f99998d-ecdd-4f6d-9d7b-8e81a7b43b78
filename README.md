# ContentFlow

ContentFlow 是一个基于 Next.js 15 构建的AI驱动的跨平台内容创作与发布管理工具，专为内容创作者、小型营销团队和自由职业者设计。通过统一的工作台解决多平台内容管理复杂性，提升创作效率，优化内容表现。

## 🌟 核心特性

- 🤖 **AI驱动优化**: 基于OpenAI GPT-4的智能文案生成和内容优化
- 🔄 **跨平台管理**: 统一管理Instagram、TikTok、LinkedIn、Twitter等平台
- 📝 **智能编辑器**: 支持文本、图片、视频的统一编辑和实时预览
- � **格式自适配**: 一键生成适配不同平台的内容格式
- ⏰ **智能调度**: 基于最佳时机的定时发布系统
- 📊 **数据分析**: 统一的多平台数据分析和表现预测
- 🔐 **安全认证**: NextAuth.js v4 支持 Google、GitHub、邮箱密码登录
- 💳 **订阅系统**: Stripe 支付集成，支持多层级订阅计划
- 🌐 **国际化支持**: 基于 next-intl 的完整国际化方案 (支持中英文)
- 📱 **响应式设计**: 完全适配移动端和桌面端
- 🧪 **测试覆盖**: 自动化测试方案
- 🎯 **SEO优化**: 完整的元数据和页面优化

## 🛠️ 技术栈

### 核心框架
- **Next.js 15.0.3** - React 全栈框架，支持 App Router
- **React 19.0.0-rc** - 最新的 React 版本
- **TypeScript 5.x** - 类型安全的 JavaScript

### AI 与内容处理
- **OpenAI GPT-4 API** - 智能文案生成和内容优化
- **Sharp.js** - 高性能图像处理
- **FFmpeg** - 视频处理和格式转换

### UI 框架与组件
- **Tailwind CSS 3.4.1** - 原子化 CSS 框架
- **Radix UI** - 无障碍的 UI 组件库
  - Accordion, Dialog, Dropdown Menu, Avatar, Badge 等
- **Framer Motion** - 强大的动画库
- **Lucide React** - 现代化图标库
- **Shadcn/ui** - 基于 Radix UI 的组件系统

### 社交平台集成
- **Instagram Basic Display API** - Instagram 内容发布和数据获取
- **TikTok for Developers** - TikTok 内容管理
- **LinkedIn Marketing API** - LinkedIn 商业内容发布
- **Twitter API v2** - Twitter/X 平台集成

### 认证与安全
- **NextAuth.js v4** - 完整的认证解决方案
  - Google OAuth 登录
  - GitHub OAuth 登录
  - Google One Tap 登录
  - 邮箱密码登录
- **bcryptjs** - 密码加密
- **JWT** - 安全令牌管理

### 支付系统
- **Stripe 17.5.0** - 完整的支付解决方案
  - 订阅付款管理
  - Webhook 处理
  - 订单管理
  - 多层级定价

### 数据库与ORM
- **Prisma 6.1.0** - 现代化 ORM
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **Prisma Studio** - 数据库可视化管理

### 云服务与存储
- **AWS S3** - 媒体文件存储
- **AWS CloudFront** - CDN 加速
- **Vercel** - 应用部署平台

### 国际化
- **next-intl 3.26.3** - Next.js 国际化解决方案
- 支持中文和英文
- 动态路由本地化

### 开发工具
- **Turbopack** - 极速构建工具
- **ESLint** - 代码质量检查
- **Jest** - 单元测试框架
- **Docker** - 容器化部署

## 📋 环境要求

- **Node.js 18.17+** - JavaScript 运行环境
- **pnpm 8.0+** - 包管理器（推荐）
- **PostgreSQL 13+** - 主数据库
- **Redis** - 缓存服务器
- **Docker** - 容器化部署（可选）

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/wenhaofree/contentflow-web.git
cd contentflow-web
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 环境变量配置

```bash
cp .env.example .env.local
```

配置以下环境变量：

| 变量名 | 说明 | 示例 | 必需 |
|-------|------|------|------|
| **数据库配置** |
| `DATABASE_URL` | PostgreSQL数据库连接URL | `********************************/contentflow_db` | ✅ |
| `REDIS_URL` | Redis缓存连接URL | `redis://localhost:6379` | ✅ |
| **认证配置** |
| `NEXTAUTH_SECRET` | NextAuth.js 密钥 | `your-super-secret-key` | ✅ |
| `NEXTAUTH_URL` | 应用URL | `http://localhost:3000` | ✅ |
| **AI服务配置** |
| `OPENAI_API_KEY` | OpenAI API密钥 | `sk-xxx` | ✅ |
| **Google OAuth** |
| `AUTH_GOOGLE_ID` | Google OAuth ID | `google-oauth-id` | ❌ |
| `AUTH_GOOGLE_SECRET` | Google OAuth Secret | `google-oauth-secret` | ❌ |
| `NEXT_PUBLIC_AUTH_GOOGLE_ENABLED` | 启用Google登录 | `true` | ❌ |
| `NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED` | 启用Google One Tap | `true` | ❌ |
| **GitHub OAuth** |
| `AUTH_GITHUB_ID` | GitHub OAuth ID | `github-oauth-id` | ❌ |
| `AUTH_GITHUB_SECRET` | GitHub OAuth Secret | `github-oauth-secret` | ❌ |
| `NEXT_PUBLIC_AUTH_GITHUB_ENABLED` | 启用GitHub登录 | `true` | ❌ |
| **社交平台API** |
| `INSTAGRAM_CLIENT_ID` | Instagram客户端ID | `instagram-client-id` | ❌ |
| `INSTAGRAM_CLIENT_SECRET` | Instagram客户端密钥 | `instagram-client-secret` | ❌ |
| `TIKTOK_CLIENT_KEY` | TikTok客户端密钥 | `tiktok-client-key` | ❌ |
| `TIKTOK_CLIENT_SECRET` | TikTok客户端密钥 | `tiktok-client-secret` | ❌ |
| `LINKEDIN_CLIENT_ID` | LinkedIn客户端ID | `linkedin-client-id` | ❌ |
| `LINKEDIN_CLIENT_SECRET` | LinkedIn客户端密钥 | `linkedin-client-secret` | ❌ |
| `TWITTER_CLIENT_ID` | Twitter客户端ID | `twitter-client-id` | ❌ |
| `TWITTER_CLIENT_SECRET` | Twitter客户端密钥 | `twitter-client-secret` | ❌ |
| **云存储配置** |
| `AWS_ACCESS_KEY_ID` | AWS访问密钥ID | `aws-access-key` | ❌ |
| `AWS_SECRET_ACCESS_KEY` | AWS访问密钥 | `aws-secret-key` | ❌ |
| `AWS_S3_BUCKET` | S3存储桶名称 | `contentflow-media` | ❌ |
| **Stripe 支付** |
| `NEXT_PUBLIC_STRIPE_PUBLIC_KEY` | Stripe 公钥 | `pk_test_xxx` | ❌ |
| `STRIPE_PRIVATE_KEY` | Stripe 私钥 | `sk_test_xxx` | ❌ |
| `STRIPE_WEBHOOK_SECRET` | Stripe Webhook 密钥 | `whsec_xxx` | ❌ |

### 4. 数据库初始化

```bash
# 拉取数据库架构
pnpm db:pull

# 推送架构变更
pnpm db:push

# 生成Prisma Client
pnpm db:generate

# 或者一键同步
pnpm db:sync
```

### 5. 启动开发服务器

```bash
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用

## 📚 功能详解

### 🤖 AI驱动的内容优化

ContentFlow 集成先进的AI技术，提供智能内容创作支持：

1. **智能文案生成**
   - 基于OpenAI GPT-4的文案自动生成
   - 支持不同平台的语调和风格适配
   - 提供多个文案版本供选择

2. **内容质量评估**
   - 简单性评分：分析内容复杂度
   - 具体性评分：评估数据和案例使用
   - 情感性评分：识别情感触发点
   - Hook质量：预测前3秒吸引力

3. **优化建议**
   - 基于历史表现数据的改进建议
   - 标签和关键词智能推荐
   - 发布时机优化建议

### � 跨平台内容管理

统一管理多个社交媒体平台：

1. **支持的平台**
   - Instagram: Feed、Stories、Reels
   - TikTok: 短视频内容
   - LinkedIn: 专业内容发布
   - Twitter/X: 文字和图片内容

2. **格式自适配**
   - 自动调整图片尺寸和比例
   - 智能裁剪和缩放
   - 文案长度自动适配
   - 平台特定的标签建议

3. **批量发布**
   - 一键发布到多个平台
   - 定时发布调度
   - 发布状态实时跟踪

### 📊 数据分析中心

提供全面的内容表现分析：

1. **关键指标监控**
   - 互动率：点赞、评论、分享、保存
   - 覆盖率：展示次数、独立访客
   - 转化率：点击链接、关注转化
   - 增长趋势：粉丝增长、内容表现

2. **内容排行榜**
   - 高表现内容识别
   - 内容类型分析
   - 最佳发布时间统计

3. **预测分析**
   - 内容表现预测
   - 受众活跃度分析
   - 竞品表现对比

### �🔐 认证系统

项目支持多种登录方式：

1. **Google OAuth 登录**
   - 配置 `AUTH_GOOGLE_ID` 和 `AUTH_GOOGLE_SECRET`
   - 设置 `NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true`

2. **GitHub OAuth 登录**
   - 配置 `AUTH_GITHUB_ID` 和 `AUTH_GITHUB_SECRET`
   - 设置 `NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true`

3. **Google One Tap 登录**
   - 设置 `NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED=true`

4. **邮箱密码登录**
   - 默认启用，支持用户注册和登录

### 💳 订阅系统

集成 Stripe 支付，支持多层级订阅：

1. **免费版** ($0/月)
   - 2个社交账户
   - 50条内容/月
   - 基础数据分析

2. **创作者版** ($29/月)
   - 5个社交账户
   - 无限内容发布
   - AI文案生成
   - 高级数据分析

3. **团队版** ($99/月)
   - 团队协作功能
   - 客户管理系统
   - 优先技术支持

4. **机构版** ($299/月)
   - 无限用户
   - 白标定制
   - 专属客户经理

### 🌐 国际化

- 支持中文 (`zh`) 和英文 (`en`)
- 动态路由: `/en/...` 和 `/zh/...`
- 自动语言检测
- 完整的翻译文件管理

### 📊 数据库设计

使用 Prisma ORM，包含以下主要模型：

- **User**: 用户信息，支持多种登录方式
- **SocialAccount**: 社交媒体账户连接信息
- **Content**: 内容创作和管理
- **Publication**: 发布记录和状态跟踪
- **Analytics**: 数据分析和统计
- **Subscription**: 订阅和支付管理

## 🛠️ 可用的脚本命令

### 开发命令
```bash
pnpm dev          # 开发环境启动（使用Turbopack）
pnpm build        # 生产环境构建
pnpm start        # 生产环境启动
pnpm lint         # ESLint 代码检查
```

### 数据库命令
```bash
pnpm db:push      # 推送数据库变更
pnpm db:pull      # 拉取数据库架构
pnpm db:generate  # 生成Prisma Client
pnpm db:studio    # 启动Prisma Studio
pnpm db:sync      # 同步数据库架构
```

### 测试命令
```bash
pnpm test:db            # 运行数据库测试
pnpm test:db:docker     # 使用Docker运行数据库测试
pnpm docker:up          # 启动Docker容器
pnpm docker:down        # 停止Docker容器
```

## 📁 项目结构

```
ContentFlow/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── [locale]/          # 国际化路由
│   │   │   ├── auth/          # 认证页面
│   │   │   ├── dashboard/     # 用户仪表板
│   │   │   ├── creator/       # 内容创作工作台
│   │   │   ├── analytics/     # 数据分析页面
│   │   │   ├── pricing/       # 定价页面
│   │   │   └── page.tsx       # 首页
│   │   ├── api/               # API 路由
│   │   │   ├── auth/          # NextAuth.js 认证
│   │   │   ├── ai/            # AI 服务 API
│   │   │   ├── social/        # 社交平台 API
│   │   │   ├── content/       # 内容管理 API
│   │   │   ├── analytics/     # 数据分析 API
│   │   │   ├── stripe/        # Stripe 支付
│   │   │   └── user/          # 用户相关 API
│   │   ├── actions.ts         # Server Actions
│   │   ├── globals.css        # 全局样式
│   │   └── providers.tsx      # 全局 Provider
│   ├── components/            # React 组件
│   │   ├── creator/           # 内容创作组件
│   │   │   ├── ContentEditor.tsx    # 内容编辑器
│   │   │   ├── AIAssistant.tsx      # AI 助手
│   │   │   ├── PlatformPreview.tsx  # 平台预览
│   │   │   └── PublishScheduler.tsx # 发布调度器
│   │   ├── analytics/         # 数据分析组件
│   │   │   ├── Dashboard.tsx        # 分析仪表板
│   │   │   ├── MetricsCard.tsx      # 指标卡片
│   │   │   ├── TrendChart.tsx       # 趋势图表
│   │   │   └── ContentRanking.tsx   # 内容排行
│   │   ├── social/            # 社交平台组件
│   │   │   ├── AccountManager.tsx   # 账户管理
│   │   │   ├── PlatformCard.tsx     # 平台卡片
│   │   │   └── ConnectionStatus.tsx # 连接状态
│   │   ├── sections/          # 页面区块组件
│   │   │   ├── Hero.tsx       # 首页横幅
│   │   │   ├── Features.tsx   # 功能展示
│   │   │   ├── Pricing.tsx    # 定价表格
│   │   │   ├── Stats.tsx      # 数据统计
│   │   │   ├── Testimonial.tsx # 用户评价
│   │   │   ├── FAQ.tsx        # 常见问题
│   │   │   └── CTA.tsx        # 行动号召
│   │   ├── ui/                # UI 基础组件
│   │   │   ├── button.tsx     # 按钮组件
│   │   │   ├── bento-grid.tsx # Bento 网格
│   │   │   ├── footer-section.tsx # 页脚
│   │   │   └── splash-cursor.tsx # 光标效果
│   │   ├── Header.tsx         # 导航栏
│   │   └── GoogleOneTapWrapper.tsx # Google One Tap
│   ├── lib/                   # 工具函数
│   │   ├── auth.ts            # 认证配置
│   │   ├── db.ts              # 数据库连接
│   │   ├── ai/                # AI 服务
│   │   │   ├── openai.ts      # OpenAI 集成
│   │   │   └── content-optimizer.ts # 内容优化
│   │   ├── social/            # 社交平台集成
│   │   │   ├── instagram.ts   # Instagram API
│   │   │   ├── tiktok.ts      # TikTok API
│   │   │   ├── linkedin.ts    # LinkedIn API
│   │   │   └── twitter.ts     # Twitter API
│   │   ├── stripe.ts          # Stripe 配置
│   │   └── utils.ts           # 通用工具
│   ├── i18n/                  # 国际化配置
│   │   ├── messages/          # 翻译文件
│   │   │   ├── en.json        # 英文翻译
│   │   │   └── zh.json        # 中文翻译
│   │   └── routing.ts         # 路由配置
│   └── types/                 # TypeScript 类型定义
│       ├── social.ts          # 社交平台类型
│       ├── content.ts         # 内容类型
│       └── analytics.ts       # 分析数据类型
├── prisma/                    # Prisma 配置
│   ├── schema.prisma          # 数据库模型
│   └── migrations/            # 数据库迁移
├── tests/                     # 测试文件
│   ├── db/                    # 数据库测试
│   ├── api/                   # API 测试
│   └── setup.ts               # 测试配置
├── docs/                      # 项目文档
│   ├── mvp_executive_summary.md      # 产品执行摘要
│   ├── mvp_product_design_prototype.md # 产品设计原型
│   └── mvp_technical_implementation_guide.md # 技术实现指南
├── public/                    # 静态资源
├── .env.example               # 环境变量示例
├── docker-compose.yml         # Docker 配置
├── middleware.ts              # Next.js 中间件
└── tailwind.config.ts         # Tailwind 配置
```

## ⚙️ 配置说明

### 数据库配置
- 支持 MySQL 和 PostgreSQL
- 使用 `sslmode=prefer` 进行安全连接
- Prisma ORM 提供类型安全的数据库操作

## 🔌 API 路由

### 认证相关 API
- `GET/POST /api/auth/*` - NextAuth.js 认证端点
- `POST /api/auth/register` - 用户注册
- `GET /api/user/profile` - 获取用户信息

### AI 服务 API
- `POST /api/ai/generate-content` - AI 文案生成
- `POST /api/ai/optimize-content` - 内容优化建议
- `POST /api/ai/analyze-content` - 内容质量分析
- `POST /api/ai/suggest-hashtags` - 标签推荐

### 社交平台 API
- `GET /api/social/accounts` - 获取连接的社交账户
- `POST /api/social/connect` - 连接社交账户
- `DELETE /api/social/disconnect/:id` - 断开社交账户
- `GET /api/social/platforms` - 获取支持的平台列表

### 内容管理 API
- `GET /api/content` - 获取内容列表
- `POST /api/content` - 创建新内容
- `PUT /api/content/:id` - 更新内容
- `DELETE /api/content/:id` - 删除内容
- `POST /api/content/:id/publish` - 发布内容
- `GET /api/content/:id/preview` - 预览内容

### 发布管理 API
- `POST /api/publish/schedule` - 调度发布
- `GET /api/publish/status/:id` - 获取发布状态
- `POST /api/publish/cancel/:id` - 取消发布
- `GET /api/publish/history` - 获取发布历史

### 数据分析 API
- `GET /api/analytics/overview` - 获取数据概览
- `GET /api/analytics/content/:id` - 获取内容分析数据
- `GET /api/analytics/platform/:platform` - 获取平台分析数据
- `GET /api/analytics/trends` - 获取趋势数据

### 支付相关 API
- `POST /api/stripe/create-checkout-session` - 创建支付会话
- `POST /api/stripe/webhook` - Stripe Webhook 处理
- `GET /api/stripe/subscriptions` - 获取订阅信息
- `POST /api/stripe/cancel-subscription` - 取消订阅

### 用户相关 API
- `GET /api/user/subscription` - 获取用户订阅信息
- `PUT /api/user/profile` - 更新用户信息
- `GET /api/user/usage` - 获取使用统计

## 🎯 使用指南

### 1. 内容创作工作流
1. **连接社交账户**: 在账户管理页面连接Instagram、TikTok等平台
2. **创建内容**: 使用智能编辑器创建文本、图片或视频内容
3. **AI优化**: 利用AI助手生成文案和优化建议
4. **平台适配**: 一键生成适配不同平台的内容格式
5. **调度发布**: 设置最佳发布时间或立即发布
6. **跟踪表现**: 在数据分析中心查看内容表现

### 2. 开发新功能
1. **内容创作功能**: 在 `src/components/creator/` 中添加新的创作工具
2. **AI服务集成**: 在 `src/lib/ai/` 中扩展AI功能
3. **社交平台支持**: 在 `src/lib/social/` 中添加新平台集成
4. **数据分析功能**: 在 `src/components/analytics/` 中添加新的分析组件
5. **多语言支持**: 在 `src/i18n/messages/` 中添加翻译文本

### 3. 添加新的社交平台
1. 在 `src/lib/social/` 中创建平台特定的API集成
2. 在 `src/types/social.ts` 中定义平台类型
3. 在 `src/components/social/` 中添加平台管理组件
4. 更新数据库模型以支持新平台

### 4. 扩展AI功能
1. 在 `src/lib/ai/` 中添加新的AI服务
2. 在 `src/api/ai/` 中创建相应的API端点
3. 在 `src/components/creator/` 中集成AI功能到用户界面
4. 添加相应的环境变量配置

### 5. 数据库操作
1. 在 `prisma/schema.prisma` 中定义新的数据模型
2. 运行 `pnpm db:push` 同步数据库变更
3. 使用 `src/lib/db.ts` 进行数据库操作
4. 在相应的API路由中实现数据逻辑

### 认证配置注意事项

### GitHub OAuth认证配置

配置GitHub OAuth登录时，请注意以下关键事项：

1. **GitHub OAuth应用设置**
   - 在GitHub开发者设置页面 (https://github.com/settings/developers) 创建OAuth应用
   - 应用名称设置为您的项目名称，如："NextLaunchPad"
   - Homepage URL必须与环境变量中的`NEXT_PUBLIC_WEB_URL`保持一致

2. **回调URL配置**
   - 回调URL格式：`{您的域名}/api/auth/callback/github`
   - 本地开发环境示例：`http://localhost:3000/api/auth/callback/github`
   - **注意**：`localhost`和`127.0.0.1`在OAuth认证中被视为不同域名，必须精确匹配

3. **环境变量设置**
   ```
   # GitHub认证变量必须正确设置
   AUTH_GITHUB_ID=您的GitHub客户端ID
   AUTH_GITHUB_SECRET=您的GitHub客户端密钥
   NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true

   # NEXTAUTH_URL与GitHub OAuth应用中的域名必须保持一致
   # 如果GitHub OAuth中使用localhost，这里也必须使用localhost
   NEXTAUTH_URL=http://localhost:3000
   NEXT_PUBLIC_WEB_URL=http://localhost:3000
   ```

4. **常见错误处理**
   - `redirect_uri is not associated with this application`：
     - 检查GitHub OAuth应用中的回调URL与实际使用的域名是否完全一致
     - 确保使用相同的域名格式（localhost vs 127.0.0.1）
     - 检查端口号是否匹配
   - `Missing GitHub client ID or secret`：
     - 确保环境变量中正确设置了GitHub认证凭据
     - 检查`AUTH_GITHUB_ID`和`AUTH_GITHUB_SECRET`是否与GitHub OAuth应用一致
   - `outgoing request timed out after 3500ms`（请求超时错误）：
     - 这通常是网络连接问题，而非配置错误
     - 检查您的网络连接是否稳定
     - 如果使用代理或VPN，尝试临时关闭
     - GitHub API可能暂时不可用，稍后再试
     - 如果在中国大陆地区，可能需要配置代理来访问GitHub API
     - 增加NextAuth超时配置（在auth.config.ts中添加`timeout: 10000`将超时延长到10秒）

5. **域名变更时的处理**
   - 当应用域名发生变更时（如从本地开发到生产环境）：
     - 更新GitHub OAuth应用中的回调URL
     - 或创建多个OAuth应用分别用于不同环境

## 数据库测试

项目包含了对数据库连接和表结构的自动化测试方案。

### 测试内容

- 数据库连接测试
- 表结构验证测试
- 字段类型和默认值测试
- 表关系测试
- 软删除功能测试

### 运行测试

使用本地数据库测试:

```bash
pnpm test:db
```

使用Docker独立环境测试（推荐）:

```bash
pnpm test:db:docker
```

这将：
1. 启动Docker容器中的PostgreSQL
2. 执行数据库迁移
3. 运行所有测试用例
4. 自动清理测试环境

## 🚀 部署

### Vercel 部署（推荐）

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwenhaofree%2Fcontentflow-web&env=DATABASE_URL,NEXTAUTH_SECRET,OPENAI_API_KEY,AUTH_GOOGLE_ID,AUTH_GOOGLE_SECRET,AUTH_GITHUB_ID,AUTH_GITHUB_SECRET,NEXT_PUBLIC_STRIPE_PUBLIC_KEY,STRIPE_PRIVATE_KEY,STRIPE_WEBHOOK_SECRET&project-name=contentflow&repository-name=contentflow-web)

**部署步骤：**

1. **准备工作**
   ```bash
   # Fork 本项目到你的 GitHub 账户
   git clone https://github.com/wenhaofree/contentflow-web.git
   ```

2. **在 Vercel 部署**
   - 访问 [Vercel Dashboard](https://vercel.com/dashboard)
   - 点击 "New Project"
   - 导入你的 GitHub 仓库
   - 配置环境变量（见下方列表）
   - 点击 "Deploy"

3. **必需的环境变量**
   ```bash
   # 数据库
   DATABASE_URL=your_postgresql_database_url
   REDIS_URL=your_redis_url

   # 认证
   NEXTAUTH_SECRET=your_nextauth_secret
   NEXTAUTH_URL=https://your-domain.vercel.app

   # AI 服务
   OPENAI_API_KEY=your_openai_api_key

   # OAuth (可选)
   AUTH_GOOGLE_ID=your_google_client_id
   AUTH_GOOGLE_SECRET=your_google_client_secret
   AUTH_GITHUB_ID=your_github_client_id
   AUTH_GITHUB_SECRET=your_github_client_secret

   # 社交平台 API (可选)
   INSTAGRAM_CLIENT_ID=your_instagram_client_id
   INSTAGRAM_CLIENT_SECRET=your_instagram_client_secret
   TIKTOK_CLIENT_KEY=your_tiktok_client_key
   TIKTOK_CLIENT_SECRET=your_tiktok_client_secret

   # 云存储 (可选)
   AWS_ACCESS_KEY_ID=your_aws_access_key
   AWS_SECRET_ACCESS_KEY=your_aws_secret_key
   AWS_S3_BUCKET=your_s3_bucket_name

   # Stripe 支付
   NEXT_PUBLIC_STRIPE_PUBLIC_KEY=pk_live_xxx
   STRIPE_PRIVATE_KEY=sk_live_xxx
   STRIPE_WEBHOOK_SECRET=whsec_xxx
   ```

### Docker 部署

```bash
# 构建镜像
docker build -t contentflow .

# 运行容器
docker run -p 3000:3000 --env-file .env contentflow
```

### 自托管部署

```bash
# 构建项目
pnpm build

# 启动生产服务器
pnpm start
```

项目包含完整的测试套件：

### 数据库测试
```bash
# 运行数据库连接和模型测试
pnpm test:db

# 使用 Docker 环境测试（推荐）
pnpm test:db:docker
```

**测试内容包括：**
- 数据库连接测试
- 表结构验证
- 字段类型和约束测试
- 关系模型测试
- 数据操作测试

### 单元测试
```bash
# 运行所有单元测试
pnpm test

# 监听模式运行测试
pnpm test:watch
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 贡献流程
1. **Fork 项目**
   ```bash
   git clone https://github.com/wenhaofree/contentflow-web.git
   cd contentflow-web
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **开发和测试**
   ```bash
   pnpm dev          # 启动开发服务器
   pnpm test:db      # 运行测试
   pnpm lint         # 检查代码质量
   ```

4. **提交更改**
   ```bash
   git commit -m 'feat: add amazing feature'
   ```

5. **推送并创建 PR**
   ```bash
   git push origin feature/amazing-feature
   ```

### 代码规范
- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 配置的代码规范
- 为新功能添加相应的测试
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- **项目作者**: WenHaoFree
- **Email**: <EMAIL>
- **GitHub**: [https://github.com/wenhaofree](https://github.com/wenhaofree)

## 🙏 致谢

感谢以下开源项目：
- [Next.js](https://nextjs.org/) - React 全栈框架
- [OpenAI](https://openai.com/) - AI 服务提供商
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Radix UI](https://www.radix-ui.com/) - 无障碍 UI 组件
- [Prisma](https://www.prisma.io/) - 现代化 ORM
- [NextAuth.js](https://next-auth.js.org/) - 认证解决方案
- [Stripe](https://stripe.com/) - 支付处理平台

## 🎯 路线图

### Phase 1: MVP 核心功能 ✅
- [x] ✅ 用户认证系统（多种登录方式）
- [x] ✅ 基础内容编辑器
- [x] ✅ Instagram + TikTok 发布
- [x] ✅ 简单数据分析
- [x] ✅ Stripe 支付集成
- [x] ✅ 国际化支持

### Phase 2: AI 驱动功能 🔄
- [ ] 🔄 OpenAI GPT-4 集成
- [ ] 🔄 智能文案生成
- [ ] 🔄 内容质量评估
- [ ] 🔄 Hook 优化建议
- [ ] 🔄 LinkedIn + Twitter 集成

### Phase 3: 高级分析 📊
- [ ] 📊 深度数据分析
- [ ] 📊 内容表现预测
- [ ] 📊 竞品分析功能
- [ ] 📊 自定义报告生成

### Phase 4: 团队协作 👥
- [ ] � 团队权限管理
- [ ] 👥 内容审批工作流
- [ ] � 客户管理系统
- [ ] � 白标定制方案

### Phase 5: 移动端 📱
- [ ] 📱 React Native 移动应用
- [ ] � 移动端内容创作
- [ ] 📱 推送通知系统

---

⭐ 如果这个项目对你有帮助，请给它一个 Star！
