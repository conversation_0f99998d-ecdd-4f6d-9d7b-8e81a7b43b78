# ContentFlow MVP 技术实现指南
## 快速启动开发指南

### 🚀 项目初始化

#### 1. 创建项目结构
```bash
mkdir contentflow-mvp
cd contentflow-mvp

# 创建前端项目
npx create-react-app frontend --template typescript
cd frontend
npm install @reduxjs/toolkit react-redux antd styled-components axios
npm install @types/styled-components

# 创建后端项目
cd ..
mkdir backend
cd backend
npm init -y
npm install express typescript prisma @prisma/client jsonwebtoken bcryptjs cors dotenv
npm install -D @types/node @types/express @types/jsonwebtoken @types/bcryptjs ts-node nodemon
```

#### 2. 环境配置文件
```bash
# backend/.env
DATABASE_URL="postgresql://username:password@localhost:5432/contentflow_db"
JWT_SECRET="your-super-secret-jwt-key"
OPENAI_API_KEY="your-openai-api-key"
INSTAGRAM_CLIENT_ID="your-instagram-client-id"
INSTAGRAM_CLIENT_SECRET="your-instagram-client-secret"
TIKTOK_CLIENT_KEY="your-tiktok-client-key"
TIKTOK_CLIENT_SECRET="your-tiktok-client-secret"
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_S3_BUCKET="contentflow-media-bucket"
```

---

### 🗄️ 数据库设计与初始化

#### 1. Prisma Schema 设计
```prisma
// backend/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                    String    @id @default(cuid())
  email                 String    @unique
  passwordHash          String
  name                  String
  avatarUrl             String?
  subscriptionPlan      String    @default("free")
  subscriptionExpiresAt DateTime?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  socialAccounts SocialAccount[]
  contents       Content[]
  publications   Publication[]

  @@map("users")
}

model SocialAccount {
  id           String    @id @default(cuid())
  userId       String
  platform     String
  accountId    String
  accessToken  String
  refreshToken String?
  expiresAt    DateTime?
  isActive     Boolean   @default(true)
  createdAt    DateTime  @default(now())

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  publications Publication[]

  @@unique([userId, platform, accountId])
  @@map("social_accounts")
}

model Content {
  id              String   @id @default(cuid())
  userId          String
  title           String
  description     String?
  contentType     String
  mediaUrls       Json?
  platformConfigs Json?
  status          String   @default("draft")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  publications Publication[]

  @@map("contents")
}

model Publication {
  id             String    @id @default(cuid())
  contentId      String
  socialAccountId String
  platformPostId String?
  scheduledAt    DateTime?
  publishedAt    DateTime?
  status         String    @default("pending")
  createdAt      DateTime  @default(now())

  content       Content       @relation(fields: [contentId], references: [id], onDelete: Cascade)
  socialAccount SocialAccount @relation(fields: [socialAccountId], references: [id], onDelete: Cascade)

  @@map("publications")
}
```

#### 2. 数据库初始化命令
```bash
cd backend
npx prisma generate
npx prisma db push
npx prisma studio  # 可选：打开数据库管理界面
```

---

### 🔧 后端API开发

#### 1. Express服务器配置
```typescript
// backend/src/app.ts
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';

dotenv.config();

const app = express();
const prisma = new PrismaClient();

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 路由
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/users', userRoutes);
app.use('/api/v1/contents', contentRoutes);
app.use('/api/v1/publications', publicationRoutes);
app.use('/api/v1/analytics', analyticsRoutes);

// 错误处理中间件
app.use((err: any, req: any, res: any, next: any) => {
  console.error(err.stack);
  res.status(500).json({
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: '服务器内部错误'
    }
  });
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

export { prisma };
```

#### 2. 用户认证中间件
```typescript
// backend/src/middleware/auth.ts
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import { prisma } from '../app';

interface AuthRequest extends Request {
  user?: any;
}

export const authenticateToken = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      error: {
        code: 'UNAUTHORIZED',
        message: '访问令牌缺失'
      }
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    });

    if (!user) {
      return res.status(401).json({
        error: {
          code: 'UNAUTHORIZED',
          message: '用户不存在'
        }
      });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(403).json({
      error: {
        code: 'FORBIDDEN',
        message: '访问令牌无效'
      }
    });
  }
};
```

#### 3. 内容管理API
```typescript
// backend/src/routes/contents.ts
import express from 'express';
import { prisma } from '../app';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 获取用户内容列表
router.get('/', authenticateToken, async (req: any, res) => {
  try {
    const contents = await prisma.content.findMany({
      where: { userId: req.user.id },
      orderBy: { createdAt: 'desc' },
      include: {
        publications: {
          include: {
            socialAccount: true
          }
        }
      }
    });

    res.json({ data: contents });
  } catch (error) {
    res.status(500).json({
      error: {
        code: 'DATABASE_ERROR',
        message: '获取内容列表失败'
      }
    });
  }
});

// 创建新内容
router.post('/', authenticateToken, async (req: any, res) => {
  try {
    const { title, description, contentType, mediaUrls, platformConfigs } = req.body;

    const content = await prisma.content.create({
      data: {
        userId: req.user.id,
        title,
        description,
        contentType,
        mediaUrls,
        platformConfigs
      }
    });

    res.status(201).json({ data: content });
  } catch (error) {
    res.status(500).json({
      error: {
        code: 'DATABASE_ERROR',
        message: '创建内容失败'
      }
    });
  }
});

export default router;
```

---

### 🎨 前端React应用开发

#### 1. Redux Store配置
```typescript
// frontend/src/store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import { authApi } from './api/authApi';
import { contentApi } from './api/contentApi';
import authSlice from './slices/authSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    [authApi.reducerPath]: authApi.reducer,
    [contentApi.reducerPath]: contentApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      authApi.middleware,
      contentApi.middleware
    ),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

#### 2. 内容编辑器组件
```typescript
// frontend/src/components/ContentEditor.tsx
import React, { useState } from 'react';
import { Card, Input, Upload, Button, Select, Row, Col } from 'antd';
import { UploadOutlined, SendOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { TextArea } = Input;
const { Option } = Select;

const EditorContainer = styled.div`
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
`;

const PreviewCard = styled(Card)`
  margin-left: 16px;
  .ant-card-body {
    padding: 16px;
  }
`;

interface ContentEditorProps {
  onPublish: (content: any) => void;
}

const ContentEditor: React.FC<ContentEditorProps> = ({ onPublish }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [mediaFiles, setMediaFiles] = useState<any[]>([]);

  const handlePublish = () => {
    const content = {
      title,
      description,
      platforms: selectedPlatforms,
      mediaFiles
    };
    onPublish(content);
  };

  return (
    <EditorContainer>
      <Row gutter={16}>
        <Col span={12}>
          <Card title="内容编辑器" style={{ marginBottom: 16 }}>
            <Input
              placeholder="输入内容标题"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              style={{ marginBottom: 16 }}
            />
            
            <TextArea
              placeholder="输入内容描述"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={6}
              style={{ marginBottom: 16 }}
            />
            
            <Upload
              multiple
              beforeUpload={() => false}
              onChange={(info) => setMediaFiles(info.fileList)}
            >
              <Button icon={<UploadOutlined />}>上传媒体文件</Button>
            </Upload>
          </Card>

          <Card title="发布设置">
            <Select
              mode="multiple"
              placeholder="选择发布平台"
              value={selectedPlatforms}
              onChange={setSelectedPlatforms}
              style={{ width: '100%', marginBottom: 16 }}
            >
              <Option value="instagram">Instagram</Option>
              <Option value="tiktok">TikTok</Option>
              <Option value="linkedin">LinkedIn</Option>
              <Option value="twitter">Twitter</Option>
            </Select>
            
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handlePublish}
              disabled={!title || !description || selectedPlatforms.length === 0}
              block
            >
              发布内容
            </Button>
          </Card>
        </Col>

        <Col span={12}>
          <PreviewCard title="实时预览">
            {selectedPlatforms.map(platform => (
              <div key={platform} style={{ marginBottom: 16 }}>
                <h4>{platform.toUpperCase()} 预览</h4>
                <div style={{ 
                  border: '1px solid #d9d9d9', 
                  padding: 12, 
                  borderRadius: 6,
                  backgroundColor: '#fff'
                }}>
                  <strong>{title}</strong>
                  <p>{description}</p>
                  {mediaFiles.length > 0 && (
                    <div>📷 {mediaFiles.length} 个媒体文件</div>
                  )}
                </div>
              </div>
            ))}
          </PreviewCard>
        </Col>
      </Row>
    </EditorContainer>
  );
};

export default ContentEditor;
```

---

### 🔗 社交平台API集成

#### 1. Instagram API集成
```typescript
// backend/src/services/instagramService.ts
import axios from 'axios';

export class InstagramService {
  private baseURL = 'https://graph.instagram.com';

  async publishPost(accessToken: string, content: any) {
    try {
      // 1. 创建媒体容器
      const mediaResponse = await axios.post(
        `${this.baseURL}/me/media`,
        {
          image_url: content.mediaUrl,
          caption: content.caption,
          access_token: accessToken
        }
      );

      const creationId = mediaResponse.data.id;

      // 2. 发布媒体
      const publishResponse = await axios.post(
        `${this.baseURL}/me/media_publish`,
        {
          creation_id: creationId,
          access_token: accessToken
        }
      );

      return {
        success: true,
        postId: publishResponse.data.id
      };
    } catch (error) {
      console.error('Instagram发布失败:', error);
      return {
        success: false,
        error: error.response?.data || error.message
      };
    }
  }

  async getAccountInfo(accessToken: string) {
    try {
      const response = await axios.get(
        `${this.baseURL}/me?fields=id,username,account_type,media_count&access_token=${accessToken}`
      );
      return response.data;
    } catch (error) {
      throw new Error('获取Instagram账户信息失败');
    }
  }
}
```

#### 2. TikTok API集成
```typescript
// backend/src/services/tiktokService.ts
import axios from 'axios';

export class TikTokService {
  private baseURL = 'https://open-api.tiktok.com';

  async uploadVideo(accessToken: string, videoFile: Buffer, caption: string) {
    try {
      // 1. 初始化上传
      const initResponse = await axios.post(
        `${this.baseURL}/share/video/upload/`,
        {
          access_token: accessToken
        }
      );

      const uploadUrl = initResponse.data.data.upload_url;

      // 2. 上传视频文件
      const uploadResponse = await axios.put(uploadUrl, videoFile, {
        headers: {
          'Content-Type': 'video/mp4'
        }
      });

      // 3. 发布视频
      const publishResponse = await axios.post(
        `${this.baseURL}/share/video/publish/`,
        {
          access_token: accessToken,
          video_id: uploadResponse.data.video.video_id,
          text: caption,
          privacy_level: 'SELF_ONLY', // 或 'PUBLIC'
          disable_duet: false,
          disable_comment: false,
          disable_stitch: false
        }
      );

      return {
        success: true,
        shareId: publishResponse.data.data.share_id
      };
    } catch (error) {
      console.error('TikTok发布失败:', error);
      return {
        success: false,
        error: error.response?.data || error.message
      };
    }
  }
}
```

---

### 🤖 AI功能集成

#### 1. OpenAI内容优化服务
```typescript
// backend/src/services/aiService.ts
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

export class AIService {
  async optimizeContent(content: string, platform: string) {
    try {
      const prompt = `
        请优化以下社交媒体内容，使其更适合${platform}平台：
        
        原内容：${content}
        
        优化要求：
        1. 简洁明了，易于理解
        2. 包含具体数据或案例
        3. 能够触发情感共鸣
        4. 适合${platform}平台的特点
        
        请返回优化后的内容：
      `;

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的社交媒体内容优化专家。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.7
      });

      return {
        optimizedContent: response.choices[0].message.content,
        suggestions: this.generateSuggestions(content, platform)
      };
    } catch (error) {
      console.error('AI内容优化失败:', error);
      throw new Error('AI服务暂时不可用');
    }
  }

  private generateSuggestions(content: string, platform: string) {
    // 基于平台特性生成建议
    const suggestions = [];
    
    if (platform === 'instagram') {
      suggestions.push('建议添加相关话题标签');
      suggestions.push('考虑使用高质量的视觉素材');
    }
    
    if (platform === 'tiktok') {
      suggestions.push('保持视频时长在15-60秒');
      suggestions.push('使用流行音乐或音效');
    }
    
    return suggestions;
  }

  async generateHashtags(content: string, platform: string) {
    try {
      const prompt = `
        为以下${platform}内容生成相关的话题标签：
        
        内容：${content}
        
        请生成5-10个相关的话题标签，格式为 #标签名：
      `;

      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 200,
        temperature: 0.5
      });

      const hashtags = response.choices[0].message.content
        ?.split('\n')
        .filter(line => line.includes('#'))
        .map(line => line.trim());

      return hashtags || [];
    } catch (error) {
      console.error('生成话题标签失败:', error);
      return [];
    }
  }
}
```

---

### 📊 数据分析功能

#### 1. 分析数据收集
```typescript
// backend/src/services/analyticsService.ts
import { prisma } from '../app';

export class AnalyticsService {
  async collectPostMetrics(publicationId: string, platform: string) {
    try {
      // 根据不同平台调用相应的API获取数据
      let metrics;
      
      switch (platform) {
        case 'instagram':
          metrics = await this.getInstagramMetrics(publicationId);
          break;
        case 'tiktok':
          metrics = await this.getTikTokMetrics(publicationId);
          break;
        default:
          throw new Error(`不支持的平台: ${platform}`);
      }

      // 保存到数据库
      await prisma.postMetrics.create({
        data: {
          publicationId,
          platform,
          likes: metrics.likes,
          comments: metrics.comments,
          shares: metrics.shares,
          views: metrics.views,
          reach: metrics.reach,
          collectedAt: new Date()
        }
      });

      return metrics;
    } catch (error) {
      console.error('收集数据失败:', error);
      throw error;
    }
  }

  async getUserAnalytics(userId: string, timeRange: string) {
    const startDate = this.getStartDate(timeRange);
    
    const analytics = await prisma.postMetrics.findMany({
      where: {
        publication: {
          content: {
            userId
          }
        },
        collectedAt: {
          gte: startDate
        }
      },
      include: {
        publication: {
          include: {
            content: true,
            socialAccount: true
          }
        }
      }
    });

    return this.processAnalyticsData(analytics);
  }

  private processAnalyticsData(analytics: any[]) {
    const totalMetrics = {
      totalLikes: 0,
      totalComments: 0,
      totalShares: 0,
      totalViews: 0,
      totalReach: 0,
      postCount: analytics.length
    };

    const platformBreakdown: any = {};
    const topPerformingPosts: any[] = [];

    analytics.forEach(metric => {
      // 累计总数
      totalMetrics.totalLikes += metric.likes || 0;
      totalMetrics.totalComments += metric.comments || 0;
      totalMetrics.totalShares += metric.shares || 0;
      totalMetrics.totalViews += metric.views || 0;
      totalMetrics.totalReach += metric.reach || 0;

      // 平台分析
      if (!platformBreakdown[metric.platform]) {
        platformBreakdown[metric.platform] = {
          likes: 0,
          comments: 0,
          shares: 0,
          views: 0,
          postCount: 0
        };
      }
      
      platformBreakdown[metric.platform].likes += metric.likes || 0;
      platformBreakdown[metric.platform].comments += metric.comments || 0;
      platformBreakdown[metric.platform].shares += metric.shares || 0;
      platformBreakdown[metric.platform].views += metric.views || 0;
      platformBreakdown[metric.platform].postCount += 1;

      // 高表现内容
      const engagementRate = ((metric.likes + metric.comments + metric.shares) / (metric.views || 1)) * 100;
      topPerformingPosts.push({
        ...metric,
        engagementRate
      });
    });

    // 排序高表现内容
    topPerformingPosts.sort((a, b) => b.engagementRate - a.engagementRate);

    return {
      totalMetrics,
      platformBreakdown,
      topPerformingPosts: topPerformingPosts.slice(0, 10),
      averageEngagementRate: totalMetrics.postCount > 0 
        ? ((totalMetrics.totalLikes + totalMetrics.totalComments + totalMetrics.totalShares) / totalMetrics.totalViews) * 100 
        : 0
    };
  }

  private getStartDate(timeRange: string): Date {
    const now = new Date();
    switch (timeRange) {
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case '90d':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      default:
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
  }
}
```

---

### 🚀 部署配置

#### 1. Docker配置
```dockerfile
# backend/Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npx prisma generate

EXPOSE 3001

CMD ["npm", "start"]
```

#### 2. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: contentflow_db
      POSTGRES_USER: contentflow
      POSTGRES_PASSWORD: password123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    ports:
      - "3001:3001"
    environment:
      DATABASE_URL: **************************************************/contentflow_db
      REDIS_URL: redis://redis:6379
    depends_on:
      - postgres
      - redis

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend

volumes:
  postgres_data:
```

#### 3. 部署脚本
```bash
#!/bin/bash
# deploy.sh

echo "开始部署ContentFlow MVP..."

# 构建和启动服务
docker-compose down
docker-compose build
docker-compose up -d

# 等待数据库启动
sleep 10

# 运行数据库迁移
docker-compose exec backend npx prisma db push

echo "部署完成！"
echo "前端地址: http://localhost:3000"
echo "后端API: http://localhost:3001"
```

---

### 📝 开发检查清单

#### MVP核心功能清单
- [ ] 用户注册登录系统
- [ ] 社交账户连接 (Instagram, TikTok)
- [ ] 基础内容编辑器
- [ ] 跨平台内容发布
- [ ] 简单数据统计
- [ ] 用户界面优化
- [ ] 基础错误处理
- [ ] 数据库设计和实现

#### 测试清单
- [ ] 单元测试 (后端API)
- [ ] 集成测试 (数据库操作)
- [ ] 端到端测试 (用户流程)
- [ ] 性能测试 (并发处理)
- [ ] 安全测试 (认证授权)

#### 部署清单
- [ ] 环境变量配置
- [ ] 数据库迁移
- [ ] SSL证书配置
- [ ] 监控和日志
- [ ] 备份策略

---

这个技术实现指南提供了ContentFlow MVP开发的完整技术路径，包括项目初始化、数据库设计、API开发、前端实现、第三方集成和部署配置。开发团队可以按照这个指南逐步实现MVP功能。
